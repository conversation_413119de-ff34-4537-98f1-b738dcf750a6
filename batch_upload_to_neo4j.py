"""
大量上傳判決書到 Neo4j
"""
import asyncio
import json
import os
from pathlib import Path

from dotenv import load_dotenv
from neo4j import GraphDatabase

from verdict_graph import classification_to_cypher, process_judgment

# 讀入 Neo4j 資訊
load_dotenv()
NEO4J_URI = os.getenv("NEO4J_URI", "bolt://localhost:7687")
NEO4J_USER = os.getenv("NEO4J_USER", "neo4j")
NEO4J_PASSWORD = os.getenv("NEO4J_PASSWORD")

# 路徑設定
SAMPLE_DIR = Path("sample_500")
OUTPUT_DIR = Path("cypher_output")
OUTPUT_DIR.mkdir(exist_ok=True)

# 建立 Neo4j 連線
driver = GraphDatabase.driver(NEO4J_URI, auth=(NEO4J_USER, NEO4J_PASSWORD))

def run_cypher_in_neo4j(cypher_query: str):
    with driver.session() as session:
        session.run(cypher_query)

async def process_and_upload(file_path: Path):
    try:
        print(f"📄 處理: {file_path.name}")

        result = await process_judgment(str(file_path))
        cypher_query = classification_to_cypher(result)

        # 寫入 Neo4j
        run_cypher_in_neo4j(cypher_query)
        print("✅ 已寫入 Neo4j")

        # 備份 .cypher
        output_path = OUTPUT_DIR / (file_path.stem + ".cypher")
        output_path.write_text(cypher_query, encoding="utf-8")
        print(f"💾 備份：{output_path.name}")

    except Exception as e:
        print(f"❌ 失敗: {file_path.name} - {e}")

async def main():
    files = list(SAMPLE_DIR.glob("*.json"))
    for file_path in files:
        await process_and_upload(file_path)

    driver.close()

if __name__ == "__main__":
    asyncio.run(main())
