{"cells": [{"cell_type": "markdown", "id": "d3350a64", "metadata": {}, "source": ["# 判決書分析"]}, {"cell_type": "markdown", "id": "b1297cae", "metadata": {}, "source": ["## <PERSON><PERSON> file"]}, {"cell_type": "code", "execution_count": 26, "id": "f7cc5202", "metadata": {}, "outputs": [], "source": ["# 不使用JsonLoader..我發現自己轉成Document還比較知道過程發生了什麼，也方便自己加東加西\n", "from langchain_core.documents import Document\n", "import json\n", "from pathlib import Path\n", "from pprint import pprint\n", "\n", "\n", "file_path=Path('./2024裁判書/202401/臺灣臺中地方法院刑事/TCDM,111,中簡,2601,********,1.json')\n", "with file_path.open(encoding=\"utf-8\") as f:\n", "    data = json.load(f)"]}, {"cell_type": "code", "execution_count": 27, "id": "fc03384f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'JCASE': '中簡',\n", " 'JDATE': '********',\n", " 'JFULL': '臺灣臺中地方法院刑事簡易判決\\r\\n'\n", "          '111年度中簡字第2601號\\r\\n'\n", "          '聲  請  人  臺灣臺中地方檢察署檢察官\\r\\n'\n", "          '被      告  彭如鈴\\r\\n'\n", "          '\\r\\n'\n", "          '\\r\\n'\n", "          '\\r\\n'\n", "          '\\r\\n'\n", "          '上列被告因詐欺等案件，經檢察官聲請以簡易判決處刑（111年\\r\\n'\n", "          '度偵字第38882號），本院判決如下：\\r\\n'\n", "          '    主      文\\r\\n'\n", "          '彭如鈴犯侵占遺失物罪，處罰金新臺幣捌仟元，如易服勞役，以\\r\\n'\n", "          '新臺幣壹仟元折算壹日。又犯詐欺取財罪，處拘役貳拾日，如易\\r\\n'\n", "          '科罰金，以新臺幣壹仟元折算壹日。\\r\\n'\n", "          '未扣案犯罪所得即價值新臺幣壹佰元之汽油沒收，於全部或一部\\r\\n'\n", "          '不能沒收或不宜執行沒收時，追徵其價額。\\r\\n'\n", "          '    犯罪事實及理由\\r\\n'\n", "          '一、彭如鈴分別為下列犯行：\\r\\n'\n", "          '  ㈠於民國111年6月19日4時36分許不久前某時許，在不詳地點，\\r\\n'\n", "          '    拾獲林佳穎所申設之中國信託商業銀行卡號4477-XXXX-XXXX\\r\\n'\n", "          '    -4947號金融卡（真實卡號詳卷，下稱系爭金融卡），竟未\\r\\n'\n", "          '    報警亦未揭示招領，即意圖為自己不法之所有，基於侵占遺\\r\\n'\n", "          '    失物之犯意，將系爭金融卡侵占入己。\\r\\n'\n", "          '  ㈡彭如鈴侵占系爭金融卡後，另意圖為自己不法之所有，基於\\r\\n'\n", "          '    詐欺取財之犯意，於111年6月19日4時36分許，騎乘其向進\\r\\n'\n", "          '    大輪業商行承租之車牌號碼000-000號普通重型機車，前往\\r\\n'\n", "          '    臺中市○區○○路0段00號「中油加油站向上路站」，持系爭金\\r\\n'\n", "          '    融卡、冒用林佳穎名義，以小額消費免簽名之方式刷卡消費\\r\\n'\n", "          '    ，致上開加油站員工誤信其為真正持卡人，同意其刷卡消費\\r\\n'\n", "          '    新臺幣（下同）100元，以此方式詐得等價之汽油。嗣因林\\r\\n'\n", "          '    佳穎收到付款簡訊，發覺系爭金融卡遭盜刷並報警處理，經\\r\\n'\n", "          '    警調閱監視錄影畫面，循線查悉上情。案經林佳穎訴由臺中\\r\\n'\n", "          '    市政府警察局第一分局報告臺灣臺中地方檢察署檢察官偵查\\r\\n'\n", "          '    後，聲請以簡易判決處刑。\\r\\n'\n", "          '二、證據名稱：\\r\\n'\n", "          '  ㈠被告彭如鈴於檢察事務官詢問時之陳述。\\r\\n'\n", "          '  ㈡告訴人林佳穎、證人即進大輪業商行負責人洪進舟於警詢時\\r\\n'\n", "          '    之證述。\\r\\n'\n", "          '  ㈢員警職務報告、內政部警政署反詐騙諮詢專線紀錄表、受理\\r\\n'\n", "          '    案件證明單、監視錄影畫面翻拍照片、消費明細、通訊軟體\\r\\n'\n", "          '    LINE錢包付款訊息擷圖、機車出租合約書、車輛詳細資料報\\r\\n'\n", "          '    表。\\r\\n'\n", "          '三、論罪科刑\\r\\n'\n", "          '\\u3000㈠核被告所為，就一、㈠部分，係犯刑法第337條之侵占遺失物\\r\\n'\n", "          '    罪；就一、㈡部分，係犯刑法第339條第1項之詐欺取財罪。\\r\\n'\n", "          '    被告所犯前揭2罪，犯意各別，行為互殊，應予分論併罰。\\r\\n'\n", "          '\\u3000㈡爰以行為人之責任為基礎，審酌被告被告拾獲他人財物，竟\\r\\n'\n", "          '    因一時貪念，任意侵占入己，可見其對他人財產權益之尊重\\r\\n'\n", "          '    及自己守法觀念均有偏差，且不思以正當方法謀取生活上所\\r\\n'\n", "          '    需，貪圖不法利益，率爾盜用告訴人所有之系爭金融卡購物\\r\\n'\n", "          '    消費，因而獲得價值100元之汽油，所為實不足取；惟考量\\r\\n'\n", "          '    被告為本案犯行之動機、目的、手段、犯罪所生損害，酌以\\r\\n'\n", "          '    被告之前科紀錄（見卷附臺灣高等法院被告前案紀錄表），\\r\\n'\n", "          '    及大學畢業之智識程度（見被告之個人戶籍資料查詢結果）\\r\\n'\n", "          '    等一切情狀，分別量處如主文第1項所示之刑，並就侵占遺\\r\\n'\n", "          '    失物罪部分，諭知罰金如易服勞役之折算標準，及就詐欺取\\r\\n'\n", "          '    財罪部分，諭知易科罰金之折算標準，以資懲儆。\\r\\n'\n", "          '四、沒收\\r\\n'\n", "          '  ㈠按犯罪所得，屬於犯罪行為人者，沒收之；於全部或一部不\\r\\n'\n", "          '    能沒收或不宜執行沒收時，追徵其價額，刑法第38條之1第1\\r\\n'\n", "          '    項前段、第3項分別定有明文。經查，未扣案價值100元之汽\\r\\n'\n", "          '    油為被告之犯罪所得，應依刑法第38條之1第1項前段規定宣\\r\\n'\n", "          '    告沒收，於全部或一部不能沒收或不宜執行沒收時，依同條\\r\\n'\n", "          '    第3項規定，追徵其價額。\\r\\n'\n", "          '  ㈡至未扣案之系爭金融卡，考量上開物品屬個人專屬用品，倘\\r\\n'\n", "          '    告訴人申請註銷並補發，系爭金融卡即失去功用，並無任何\\r\\n'\n", "          '    財產價值，是以，本院認依刑法第38條之2第2項之規定，宣\\r\\n'\n", "          '    告沒收或追徵此部分犯罪所得欠缺刑法上之重要性，故認無\\r\\n'\n", "          '    諭知沒收、追徵之必要。\\r\\n'\n", "          '五、依刑事訴訟法第449條第1項前段、第3項、第450條第1項、\\r\\n'\n", "          '    第454條第1項，逕以簡易判決處刑如主文。\\r\\n'\n", "          '六、如不服本判決，得自收受送達之日起20日內，向本院提起上\\r\\n'\n", "          '    訴狀，上訴於本院合議庭。\\r\\n'\n", "          '本案經檢察官陳信郎聲請以簡易判決處刑。\\u3000\\u3000\\r\\n'\n", "          '中\\u3000\\u3000華\\u3000\\u3000民\\u3000\\u3000國\\u3000\\u3000113 \\u3000'\n", "          '年\\u3000\\u30001 \\u3000\\u3000月\\u3000\\u300031\\u3000\\u3000日\\r\\n'\n", "          '                  臺中簡易庭\\u3000法\\u3000官  王曼寧\\r\\n'\n", "          '以上正本證明與原本無異。\\r\\n'\n", "          '如不服本判決，應於收受送達後20日內向本院提出上訴書狀（應\\r\\n'\n", "          '附繕本）。\\r\\n'\n", "          '告訴人或被害人如不服判決，應備理由具狀向檢察官請求上訴，\\r\\n'\n", "          '上訴期間之計算，以檢察官收受判決正本之日起算。\\r\\n'\n", "          '  \\u3000\\u3000\\u3000\\u3000\\u3000\\u3000\\u3000\\u3000  \\u3000\\u3000\\u3000\\u3000\\u3000'\n", "          '書記官  蔡昀潔\\r\\n'\n", "          '中\\u3000\\u3000華\\u3000\\u3000民\\u3000\\u3000國\\u3000\\u3000113 \\u3000'\n", "          '年\\u3000\\u30002 \\u3000\\u3000月\\u3000\\u30001 \\u3000\\u3000日\\r\\n'\n", "          '附錄本判決論罪科刑法條：\\r\\n'\n", "          '中華民國刑法第337條\\r\\n'\n", "          '意圖為自己或第三人不法之所有，而侵占遺失物、漂流物或其他\\r\\n'\n", "          '離本人所持有之物者，處1萬5,000元以下罰金。\\u3000\\u3000\\u3000\\u3000\\u3000\\u3000\\u3000\\r\\n'\n", "          '中華民國刑法第339條\\r\\n'\n", "          '意圖為自己或第三人不法之所有，以詐術使人將本人或第三人之\\r\\n'\n", "          '物交付者，處5年以下有期徒刑、拘役或科或併科50萬元以下罰\\r\\n'\n", "          '金。\\r\\n'\n", "          '以前項方法得財產上不法之利益或使第三人得之者，亦同。\\r\\n'\n", "          '前2項之未遂犯罰之。',\n", " 'JID': 'TCDM,111,中簡,2601,********,1',\n", " 'JNO': '2601',\n", " 'JPDF': 'https://data.judicial.gov.tw/opendl/JDocFile/TCDM/111%2c%e4%b8%ad%e7%b0%a1%2c2601%2c********%2c1.pdf',\n", " 'JTITLE': '詐欺等',\n", " 'JYEAR': '111'}\n"]}], "source": ["pprint(data)"]}, {"cell_type": "code", "execution_count": 28, "id": "2e6d93cc", "metadata": {}, "outputs": [{"data": {"text/plain": ["Document(metadata={}, page_content='{\"content\": \"臺灣臺中地方法院刑事簡易判決\\\\r\\\\n111年度中簡字第2601號\\\\r\\\\n聲  請  人  臺灣臺中地方檢察署檢察官\\\\r\\\\n被      告  彭如鈴\\\\r\\\\n\\\\r\\\\n\\\\r\\\\n\\\\r\\\\n\\\\r\\\\n上列被告因詐欺等案件，經檢察官聲請以簡易判決處刑（111年\\\\r\\\\n度偵字第38882號），本院判決如下：\\\\r\\\\n    主      文\\\\r\\\\n彭如鈴犯侵占遺失物罪，處罰金新臺幣捌仟元，如易服勞役，以\\\\r\\\\n新臺幣壹仟元折算壹日。又犯詐欺取財罪，處拘役貳拾日，如易\\\\r\\\\n科罰金，以新臺幣壹仟元折算壹日。\\\\r\\\\n未扣案犯罪所得即價值新臺幣壹佰元之汽油沒收，於全部或一部\\\\r\\\\n不能沒收或不宜執行沒收時，追徵其價額。\\\\r\\\\n    犯罪事實及理由\\\\r\\\\n一、彭如鈴分別為下列犯行：\\\\r\\\\n  ㈠於民國111年6月19日4時36分許不久前某時許，在不詳地點，\\\\r\\\\n    拾獲林佳穎所申設之中國信託商業銀行卡號4477-XXXX-XXXX\\\\r\\\\n    -4947號金融卡（真實卡號詳卷，下稱系爭金融卡），竟未\\\\r\\\\n    報警亦未揭示招領，即意圖為自己不法之所有，基於侵占遺\\\\r\\\\n    失物之犯意，將系爭金融卡侵占入己。\\\\r\\\\n  ㈡彭如鈴侵占系爭金融卡後，另意圖為自己不法之所有，基於\\\\r\\\\n    詐欺取財之犯意，於111年6月19日4時36分許，騎乘其向進\\\\r\\\\n    大輪業商行承租之車牌號碼000-000號普通重型機車，前往\\\\r\\\\n    臺中市○區○○路0段00號「中油加油站向上路站」，持系爭金\\\\r\\\\n    融卡、冒用林佳穎名義，以小額消費免簽名之方式刷卡消費\\\\r\\\\n    ，致上開加油站員工誤信其為真正持卡人，同意其刷卡消費\\\\r\\\\n    新臺幣（下同）100元，以此方式詐得等價之汽油。嗣因林\\\\r\\\\n    佳穎收到付款簡訊，發覺系爭金融卡遭盜刷並報警處理，經\\\\r\\\\n    警調閱監視錄影畫面，循線查悉上情。案經林佳穎訴由臺中\\\\r\\\\n    市政府警察局第一分局報告臺灣臺中地方檢察署檢察官偵查\\\\r\\\\n    後，聲請以簡易判決處刑。\\\\r\\\\n二、證據名稱：\\\\r\\\\n  ㈠被告彭如鈴於檢察事務官詢問時之陳述。\\\\r\\\\n  ㈡告訴人林佳穎、證人即進大輪業商行負責人洪進舟於警詢時\\\\r\\\\n    之證述。\\\\r\\\\n  ㈢員警職務報告、內政部警政署反詐騙諮詢專線紀錄表、受理\\\\r\\\\n    案件證明單、監視錄影畫面翻拍照片、消費明細、通訊軟體\\\\r\\\\n    LINE錢包付款訊息擷圖、機車出租合約書、車輛詳細資料報\\\\r\\\\n    表。\\\\r\\\\n三、論罪科刑\\\\r\\\\n\\u3000㈠核被告所為，就一、㈠部分，係犯刑法第337條之侵占遺失物\\\\r\\\\n    罪；就一、㈡部分，係犯刑法第339條第1項之詐欺取財罪。\\\\r\\\\n    被告所犯前揭2罪，犯意各別，行為互殊，應予分論併罰。\\\\r\\\\n\\u3000㈡爰以行為人之責任為基礎，審酌被告被告拾獲他人財物，竟\\\\r\\\\n    因一時貪念，任意侵占入己，可見其對他人財產權益之尊重\\\\r\\\\n    及自己守法觀念均有偏差，且不思以正當方法謀取生活上所\\\\r\\\\n    需，貪圖不法利益，率爾盜用告訴人所有之系爭金融卡購物\\\\r\\\\n    消費，因而獲得價值100元之汽油，所為實不足取；惟考量\\\\r\\\\n    被告為本案犯行之動機、目的、手段、犯罪所生損害，酌以\\\\r\\\\n    被告之前科紀錄（見卷附臺灣高等法院被告前案紀錄表），\\\\r\\\\n    及大學畢業之智識程度（見被告之個人戶籍資料查詢結果）\\\\r\\\\n    等一切情狀，分別量處如主文第1項所示之刑，並就侵占遺\\\\r\\\\n    失物罪部分，諭知罰金如易服勞役之折算標準，及就詐欺取\\\\r\\\\n    財罪部分，諭知易科罰金之折算標準，以資懲儆。\\\\r\\\\n四、沒收\\\\r\\\\n  ㈠按犯罪所得，屬於犯罪行為人者，沒收之；於全部或一部不\\\\r\\\\n    能沒收或不宜執行沒收時，追徵其價額，刑法第38條之1第1\\\\r\\\\n    項前段、第3項分別定有明文。經查，未扣案價值100元之汽\\\\r\\\\n    油為被告之犯罪所得，應依刑法第38條之1第1項前段規定宣\\\\r\\\\n    告沒收，於全部或一部不能沒收或不宜執行沒收時，依同條\\\\r\\\\n    第3項規定，追徵其價額。\\\\r\\\\n  ㈡至未扣案之系爭金融卡，考量上開物品屬個人專屬用品，倘\\\\r\\\\n    告訴人申請註銷並補發，系爭金融卡即失去功用，並無任何\\\\r\\\\n    財產價值，是以，本院認依刑法第38條之2第2項之規定，宣\\\\r\\\\n    告沒收或追徵此部分犯罪所得欠缺刑法上之重要性，故認無\\\\r\\\\n    諭知沒收、追徵之必要。\\\\r\\\\n五、依刑事訴訟法第449條第1項前段、第3項、第450條第1項、\\\\r\\\\n    第454條第1項，逕以簡易判決處刑如主文。\\\\r\\\\n六、如不服本判決，得自收受送達之日起20日內，向本院提起上\\\\r\\\\n    訴狀，上訴於本院合議庭。\\\\r\\\\n本案經檢察官陳信郎聲請以簡易判決處刑。\\u3000\\u3000\\\\r\\\\n中\\u3000\\u3000華\\u3000\\u3000民\\u3000\\u3000國\\u3000\\u3000113 \\u3000年\\u3000\\u30001 \\u3000\\u3000月\\u3000\\u300031\\u3000\\u3000日\\\\r\\\\n                  臺中簡易庭\\u3000法\\u3000官  王曼寧\\\\r\\\\n以上正本證明與原本無異。\\\\r\\\\n如不服本判決，應於收受送達後20日內向本院提出上訴書狀（應\\\\r\\\\n附繕本）。\\\\r\\\\n告訴人或被害人如不服判決，應備理由具狀向檢察官請求上訴，\\\\r\\\\n上訴期間之計算，以檢察官收受判決正本之日起算。\\\\r\\\\n  \\u3000\\u3000\\u3000\\u3000\\u3000\\u3000\\u3000\\u3000  \\u3000\\u3000\\u3000\\u3000\\u3000書記官  蔡昀潔\\\\r\\\\n中\\u3000\\u3000華\\u3000\\u3000民\\u3000\\u3000國\\u3000\\u3000113 \\u3000年\\u3000\\u30002 \\u3000\\u3000月\\u3000\\u30001 \\u3000\\u3000日\\\\r\\\\n附錄本判決論罪科刑法條：\\\\r\\\\n中華民國刑法第337條\\\\r\\\\n意圖為自己或第三人不法之所有，而侵占遺失物、漂流物或其他\\\\r\\\\n離本人所持有之物者，處1萬5,000元以下罰金。\\u3000\\u3000\\u3000\\u3000\\u3000\\u3000\\u3000\\\\r\\\\n中華民國刑法第339條\\\\r\\\\n意圖為自己或第三人不法之所有，以詐術使人將本人或第三人之\\\\r\\\\n物交付者，處5年以下有期徒刑、拘役或科或併科50萬元以下罰\\\\r\\\\n金。\\\\r\\\\n以前項方法得財產上不法之利益或使第三人得之者，亦同。\\\\r\\\\n前2項之未遂犯罰之。\", \"metadata\": {\"title\": \"詐欺等\", \"date\": \"********\", \"id\": \"TCDM,111,中簡,2601,********,1\", \"no\": \"2601\", \"year\": \"111\", \"case\": \"中簡\", \"pdf_url\": \"https://data.judicial.gov.tw/opendl/JDocFile/TCDM/111%2c%e4%b8%ad%e7%b0%a1%2c2601%2c********%2c1.pdf\", \"judgment_year\": \"2024\", \"judgment_month\": \"01\", \"judgment_day\": \"31\", \"judgment_date\": \"2024-01-31\"}}')"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["#結合metadata,做成content\n", "\n", "# 加入自訂\n", "jdate = data.get(\"JDATE\")\n", "jyear = jdate[:4]   # 年\n", "jmonth = jdate[4:6] # 月\n", "jday = jdate[6:]    # 日\n", "\n", "content_block = {\n", "    \"content\": data.get(\"JFULL\", \"\"),\n", "    \"metadata\": {\n", "        \"title\": data.get(\"JTITLE\"),\n", "        \"date\": data.get(\"JDATE\"),\n", "        \"id\": data.get(\"JID\"),\n", "        \"no\": data.get(\"JNO\"),\n", "        \"year\": data.get(\"JYEAR\"),\n", "        \"case\": data.get(\"JCASE\"),\n", "        \"pdf_url\": data.get(\"JPDF\"),\n", "        \"judgment_year\": jyear,\n", "        \"judgment_month\": j<PERSON>h,\n", "        \"judgment_day\": jday,\n", "        \"judgment_date\": f\"{jyear}-{jmonth}-{jday}\",\n", "    }\n", "}\n", "\n", "content_text = json.dumps(content_block, ensure_ascii=False)\n", "\n", "# 建立Document\n", "doc = Document(page_content=content_text, metadata={})\n", "doc"]}, {"cell_type": "markdown", "id": "7fad8124", "metadata": {}, "source": ["## 定義要萃取的分類schema"]}, {"cell_type": "code", "execution_count": 29, "id": "4631bc49", "metadata": {}, "outputs": [], "source": ["from typing import List, Optional\n", "from pydantic import BaseModel, Field\n", "\n", "class Prosecutor(BaseModel):\n", "    name: Optional[str] = Field(None, description=\"檢察官姓名，若未具名可為 None\")\n", "    is_anonymous: Optional[bool] = Field(None, description=\"是否為匿名檢察官，匿名的特性是最後兩個字為○○\")\n", "\n", "class Judge(BaseModel):\n", "    name: Optional[str] = Field(None, description=\"法官姓名，若未具名可為 None\")\n", "    is_presiding: Optional[bool] = Field(None, description=\"是否為審判長（主審法官）,如果本案只有一個法官，則為 True\")\n", "    is_anonymous: Optional[bool] = Field(None, description=\"是否為匿名法官，匿名的特性是姓名最後兩個字為○○\")\n", "\n", "class Clerk(BaseModel):\n", "    name: Optional[str] = Field(None, description=\"書記官姓名，若未具名可為 None\")\n", "    is_anonymous: Optional[bool] = Field(None, description=\"是否為匿名書記官，匿名的特性是最後姓名兩個字為○○\")\n", "\n", "class Person(BaseModel):\n", "    \"\"\"\n", "    只取得案件中的人物資料，不取得法官、檢察官、書記官、聲請人等職務資料\n", "    \"\"\"\n", "    name: str = Field(..., description=\"人物姓名或代稱\")\n", "    uid: Optional[str] = Field(None, description=\"人物的唯一識別碼，若有\")\n", "    role: str = Field(..., description=\"人物在案件中的角色，請使用自然語言描述，如『被告』、『證人』、『證人母親』\")\n", "    gender: Optional[str] = Field(None, description=\"人物性別（若可推論）\")\n", "    is_anonymous: Optional[bool] = Field(None, description=\"是否為匿名人物，匿名的特性是姓名最後兩個字為○○\")\n", "    relationship_to_others: Optional[str] = Field(None, description=\"與他人的關係描述，例如：『主嫌女友』\")\n", "\n", "class Organization(BaseModel):\n", "    \"\"\"\n", "    只取得案件中提到的公司或組織，不包含檢察署、法院等\n", "    \"\"\"\n", "    name: str = Field(..., description=\"組織名稱，例如保險公司、法院、醫院\")\n", "    org_type: str = Field(..., description=\"組織類型，請使用自然語言描述，如『民營保險公司』、『醫療機構』\")\n", "    related_persons: Optional[List[str]] = Field(None, description=\"與該組織有關聯的人物姓名清單\")\n", "\n", "class Location(BaseModel):\n", "    name: str = Field(..., description=\"地點名稱，例如『澎湖縣203縣道3.75公里』\")\n", "    type: Optional[str] = Field(None, description=\"地點類型，如事故現場、公司地址等\")\n", "    address: Optional[str] = Field(None, description=\"完整地址（如有）\")\n", "\n", "class Law(BaseModel):\n", "    law_name: str = Field(..., description=\"完整法條名稱，例如『刑法第185條之3第1項』\")\n", "    article: Optional[str] = Field(None, description=\"條號簡寫，例如『185-3-1』\")\n", "    description: Optional[str] = Field(None, description=\"法條摘要或罪名，例如『酒駕致人於死』\")\n", "\n", "class EventDetails(BaseModel):\n", "    amount: Optional[int] = Field(None, description=\"涉案金額，若有金流\")\n", "    bank: Optional[str] = Field(None, description=\"涉及銀行名稱\")\n", "    reason: Optional[str] = Field(None, description=\"付款理由或詐騙藉口\")\n", "    method: Optional[str] = Field(None, description=\"事件方式，例如『潑汽油引燃』\")\n", "    confession: Optional[str] = Field(None, description=\"證人或被告的供述內容\")\n", "    credibility: Optional[str] = Field(None, description=\"供述可信度（如 high / low / unknown）\")\n", "    evidence_metrics: Optional[str] = Field(None, description=\"自由文字描述的數值或檢測結果，例如『酒測值為0.34mg/L』\")\n", "\n", "class Event(BaseModel):\n", "    event_type: str = Field(..., description=\"事件類型，請使用自然語言描述，如『酒後駕車』、『縱火準備行為』\")\n", "    event_id: Optional[str] = Field(None, description=\"事件的唯一識別碼，若有\")\n", "    participants: List[str] = Field(..., description=\"參與事件的人物姓名清單\")\n", "    target_persons: Optional[List[str]] = Field(None, description=\"受害人或目標對象的姓名清單\")\n", "    location: Optional[str] = Field(None, description=\"事件發生地點的名稱\")\n", "    date_time: Optional[str] = Field(None, description=\"事件發生時間，建議使用 yyyy-mm-dd 或 yyyy-mm-dd hh:mm 格式\")\n", "    details: Optional[EventDetails] = Field(None, description=\"事件的細節描述\")\n", "\n", "class PenaltyItem(BaseModel):\n", "    type: str = Field(..., description=\"處罰類型，例如：罰金、拘役、沒收、追徵、緩刑、免訴等\")\n", "    content: str = Field(..., description=(\n", "        \"簡明描述該項處罰的重點資訊，例如金額或天數。\"\n", "        \"避免使用冗長句子，只保留數字與關鍵詞。\"\n", "        \"例如：『罰金8000元』、『拘役20日』、『沒收汽油100元』\")\n", "    )\n", "\n", "class CaseInfo(BaseModel):\n", "    case_id: str = Field(..., description=\"完整案件 ID，例如 'TCDM,111,訴,2522,20240124,2'\")\n", "    case_year: Optional[str] = Field(None, description=\"案件年度 (中華民國紀年)\")\n", "    judgment_year: Optional[str] = Field(None, description=\"判決年度 (格式yyyy)\")\n", "    judgment_month: Optional[str] = Field(None, description=\"判決月份 (格式mm)\")\n", "    judgment_day: Optional[str] = Field(None, description=\"判決日期 (格式dd)\")\n", "    case_type: str = Field(..., description=\"案件類型代碼，如『訴』、『交訴』\")\n", "    case_number: Optional[str] = Field(None, description=\"案件流水號\")\n", "    title: Optional[str] = Field(None, description=\"案件標題，例如『違反商業會計法等』\")\n", "    judgment_type: str = Field(..., description=\"判決類型（如 substantive, procedural, other）\")\n", "    verdict_items: Optional[List[PenaltyItem]] = Field(None, description=\"主文中所有處罰項目的清單，每項包含類型與簡述，例如 [{'type': '罰金', 'content': '處罰金新臺幣8000元'}]\")\n", "    judgment_date: Optional[str] = Field(None, description=\"判決日期（格式 yyyy-mm-dd）\")\n", "    court: Optional[str] = Field(None, description=\"審理法院名稱\")\n", "    pdf_url: Optional[str] = Field(None, description=\"原始判決書 PDF 下載連結\")\n", "    prosecutors: Optional[List[Prosecutor]] = Field(None, description=\"參與本案的檢察官清單\")\n", "    judges: Optional[List[Judge]] = Field(None, description=\"參與本案的法官清單\")\n", "    clerks: Optional[Clerk] = Field(None, description=\"參與本案的書記官\")\n", "\n", "class Classification(BaseModel):\n", "    case: CaseInfo = Field(..., description=\"案件的基本資料\")\n", "    people: List[Person] = Field(..., description=\"案件中的所有人物\")\n", "    organizations: Optional[List[Organization]] = Field(None, description=\"組織或公司相關資訊\")\n", "    locations: Optional[List[Location]] = Field(None, description=\"判決中提及的地點資訊\")\n", "    laws: Optional[List[Law]] = Field(None, description=\"所引用或涉及的法條\")\n", "    events: Optional[List[Event]] = Field(None, description=\"與本案有關的事件敘述與參與人\")"]}, {"cell_type": "markdown", "id": "ec308d3d", "metadata": {}, "source": ["## 初使化LLM"]}, {"cell_type": "code", "execution_count": 30, "id": "f7b141da", "metadata": {}, "outputs": [], "source": ["import getpass\n", "import os\n", "\n", "if not os.environ.get(\"OPENAI_API_KEY\"):\n", "    os.environ[\"OPENAI_API_KEY\"] = getpass.getpass(\"Enter API key for OpenAI: \")\n", "    \n", "from langchain_openai import ChatOpenAI\n", "llm = ChatOpenAI(temperature=0, model=\"gpt-4o\")"]}, {"cell_type": "markdown", "id": "4eb034e0", "metadata": {}, "source": ["## 建立Prompt templates"]}, {"cell_type": "code", "execution_count": 31, "id": "80db703f", "metadata": {}, "outputs": [], "source": ["from langchain_core.prompts import ChatPromptTemplate\n", "\n", "# basic_prompt_template = \"\"\"\n", "# 從以下判決書內容中，僅提取 {fields} 相關的資訊，並依照結構化定義回傳。\n", "# 若找不到資料，請回傳空陣列或空值，不要發明或猜測。\n", "\n", "# ⚠️ 注意：\n", "# - 若遇到需要填入人物 UID 的欄位，例如 `participants`, `target_persons`, `related_persons`，請只先回傳「人物名稱陣列」，不要轉換為 UID，會由程式自動對應。\n", "# - 例如：`participants`: [\"王小明\", \"李大民\"]\n", "# - 這些欄位仍然以 schema 結構回傳，但內容為人名。\n", "\n", "# 判決書內容：\n", "# {content}\n", "# \"\"\"\n", "\n", "basic_prompt_template = \"\"\"\n", "從以下判決書內容中，僅提取 {fields} 相關的資訊，並依照結構化定義回傳。\n", "若找不到資料，請回傳空陣列或空值，不要發明或猜測。\n", "\n", "判決書內容：\n", "{content}\n", "\"\"\"\n", "\n", "# 統一產生prompt\n", "def build_prompt(fields: str, content: str):\n", "    return ChatPromptTemplate.from_template(basic_prompt_template).invoke({\n", "        \"fields\": fields,\n", "        \"content\": content,\n", "    })\n"]}, {"cell_type": "markdown", "id": "0ba7452a", "metadata": {}, "source": ["## 定義Node"]}, {"cell_type": "code", "execution_count": null, "id": "ec55057d", "metadata": {}, "outputs": [], "source": ["from typing import Dict, List\n", "from collections import defaultdict\n", "import sys\n", "\n", "\n", "# 包裝 List 用的 Schema..with_structured_output 不能直接吃List\n", "class PeopleWrapper(BaseModel):\n", "    people: List[Person] = Field(..., description=\"案件中的所有人物\")\n", "\n", "class OrgsWrapper(BaseModel):\n", "    organizations: Optional[List[Organization]] = Field(None, description=\"組織或公司相關資訊\")\n", "\n", "class LocsWrapper(BaseModel):\n", "    locations: Optional[List[Location]] = Field(None, description=\"判決中提及的地點資訊\")\n", "\n", "class LawsWrapper(BaseModel):\n", "    laws: Optional[List[Law]] = Field(None, description=\"所引用或涉及的法條\")\n", "\n", "class EventsWrapper(BaseModel):\n", "    events: Optional[List[Event]] = Field(None, description=\"與本案有關的事件敘述與參與人\")\n", "\n", "\n", "# 安全的提取欄資料位，給有Wapper的Schema拆解用\n", "def safe_extract_field(result, field_name: str):\n", "    if isinstance(result, tuple) and result[0] == field_name:\n", "        return result[1]\n", "    elif isinstance(result, BaseModel) and hasattr(result, field_name):\n", "        return getattr(result, field_name)\n", "    else:\n", "        return []\n", "\n", "# 取得案件基本資料\n", "async def extract_case_info(state: Dict):\n", "    prompt = build_prompt(\"case資訊 (CaseInfo)\", state[\"content\"])\n", "    result = await llm.with_structured_output(CaseInfo).ainvoke(prompt)\n", "    return {\"case\": result.model_dump(), \"case_id\": result.case_id}\n", "\n", "# 取得人物資料\n", "async def extract_people(state: Dict):\n", "    prompt = build_prompt(\"人物清單 (People)\", state[\"content\"])\n", "    result = await llm.with_structured_output(PeopleWrapper).ainvoke(prompt)\n", "    people = safe_extract_field(result, \"people\")\n", "    return {\"people\": [p.model_dump() for p in people]}\n", "\n", "\n", "# 取得組織清單\n", "async def extract_organizations(state: Dict):\n", "    prompt = build_prompt(\"組織清單 (Organizations)\", state[\"content\"])\n", "    result = await llm.with_structured_output(OrgsWrapper).ainvoke(prompt)\n", "    organizations = safe_extract_field(result, \"organizations\")\n", "    return {\"organizations\": [o.model_dump() for o in organizations]}\n", "\n", "\n", "# 取得地點清單\n", "async def extract_locations(state: Dict):\n", "    prompt = build_prompt(\"地點清單 (Locations)\", state[\"content\"])\n", "    result = await llm.with_structured_output(LocsWrapper).ainvoke(prompt)\n", "    locations = safe_extract_field(result, \"locations\")\n", "    return {\"locations\": [l.model_dump() for l in locations]}\n", "\n", "# 取得法條清單\n", "async def extract_laws(state: Dict):\n", "    prompt = build_prompt(\"法條清單 (Laws)\", state[\"content\"])\n", "    result = await llm.with_structured_output(LawsWrapper).ainvoke(prompt)\n", "    laws = safe_extract_field(result, \"laws\")\n", "    return {\"laws\": [l.model_dump() for l in laws]}\n", "\n", "# 取得事件清單\n", "async def extract_events(state: Dict):\n", "    prompt = build_prompt(\"事件清單 (Events)\", state[\"content\"])\n", "    result = await llm.with_structured_output(EventsWrapper).ainvoke(prompt)\n", "    events = safe_extract_field(result, \"events\")\n", "    return {\"events\": [e.model_dump() for e in events]}\n", "\n", "# 整合所有結果  \n", "async def merge_results(state: Dict):\n", "    case = state[\"case\"]\n", "    case_id = case[\"case_id\"]\n", "\n", "    # --------- 統一補上 role / uid 給特殊角色（檢察官、法官、書記官）---------\n", "    role_defaults = {\n", "        \"prosecutors\": \"檢察官\",\n", "        \"judges\": \"法官\",\n", "        \"clerks\": \"書記官\"\n", "    }\n", "\n", "    special_people = []\n", "\n", "    for key, default_role in role_defaults.items():\n", "        value = case.get(key)\n", "        if isinstance(value, list):\n", "            for idx, item in enumerate(value, 1):\n", "                person = dict(item)\n", "                person[\"role\"] = person.get(\"role\", default_role)\n", "                special_people.append(person)\n", "        elif isinstance(value, dict):  # e.g. clerks\n", "            person = dict(value)\n", "            person[\"role\"] = person.get(\"role\", default_role)\n", "            special_people.append(person)\n", "\n", "    # --------- 合併所有人物並補上 uid ---------\n", "    people = state.get(\"people\", [])\n", "    people = [dict(p) for p in people]  # 確保是可變 dict\n", "    all_people = people + special_people\n", "\n", "    role_counter = defaultdict(int)\n", "    for person in all_people:\n", "        role = person[\"role\"].replace(\" \", \"\")\n", "        role_counter[role] += 1\n", "        person[\"uid\"] = f\"{case_id}_{role}_{role_counter[role]}\"\n", "\n", "    # --------- 移除 case 中的特殊角色欄位，避免重複 ---------\n", "    case.pop(\"prosecutors\", None)\n", "    case.pop(\"judges\", None)\n", "    case.pop(\"clerks\", None)\n", "\n", "    # --------- 建立 name → uid 對應表（只限非匿名） ---------\n", "    name_to_uid = {\n", "        p[\"name\"]: p[\"uid\"]\n", "        for p in all_people\n", "        if p.get(\"name\") and not p.get(\"is_anonymous\", False)\n", "    }\n", "\n", "    # --------- 補上 event_id，並將 name 轉為 uid ---------\n", "    events = state.get(\"events\", [])\n", "    events = [dict(ev) for ev in events]  # 確保是可變 dict\n", "    for idx, ev in enumerate(events, 1):\n", "        if not ev.get(\"event_id\"):\n", "            ev[\"event_id\"] = f\"{case_id}_ev{idx}\"\n", "\n", "        ev[\"participants\"] = [\n", "            name_to_uid.get(name, name) for name in ev.get(\"participants\", [])\n", "        ]\n", "        ev[\"target_persons\"] = [\n", "            name_to_uid.get(name, name) for name in ev.get(\"target_persons\", [])\n", "        ]\n", "\n", "    # --------- 組織裡的 related_persons 也轉成 uid ---------\n", "    organizations = state.get(\"organizations\", [])\n", "    organizations = [dict(o) for o in organizations]\n", "    for org in organizations:\n", "        org[\"related_persons\"] = [\n", "            name_to_uid.get(name, name)\n", "            for name in org.get(\"related_persons\", []) or []\n", "        ]\n", "\n", "    # --------- verdict_items 轉為 PenaltyItem 型別 ---------\n", "    verdict_items = case.get(\"verdict_items\")\n", "    if verdict_items and isinstance(verdict_items, list) and all(isinstance(v, str) for v in verdict_items):\n", "        case[\"verdict_items\"] = [\n", "            PenaltyItem(type=v.split(\":\")[0].strip(), content=v.split(\":\", 1)[1].strip())\n", "            for v in verdict_items if \":\" in v\n", "        ]\n", "\n", "    # --------- 整合成 Classification ---------\n", "    merged = {\n", "        \"case\": case,\n", "        \"people\": all_people,\n", "        \"organizations\": organizations,\n", "        \"locations\": state.get(\"locations\", []),\n", "        \"laws\": state.get(\"laws\", []),\n", "        \"events\": events,\n", "    }\n", "\n", "    return {\"classification\": Classification(**merged)}\n", "    \n", "\n"]}, {"cell_type": "markdown", "id": "cdc8dbbd", "metadata": {}, "source": ["## Compile graph"]}, {"cell_type": "code", "execution_count": 33, "id": "029975b4", "metadata": {}, "outputs": [], "source": ["from typing import TypedDict, List\n", "\n", "class JudgmentState(TypedDict, total=False):\n", "    content: str\n", "    case: dict\n", "    people: List[dict]\n", "    organizations: List[dict]\n", "    locations: List[dict]\n", "    laws: List[dict]\n", "    events: List[dict]\n", "    classification: dict\n", "\n", "from langgraph.graph import StateGraph, END\n", "\n", "graph = StateGraph(JudgmentState)\n", "\n", "graph.add_node(\"extract_case_info\", extract_case_info)\n", "graph.add_node(\"extract_people\", extract_people)\n", "graph.add_node(\"extract_organizations\", extract_organizations)\n", "graph.add_node(\"extract_locations\", extract_locations)\n", "graph.add_node(\"extract_laws\", extract_laws)\n", "graph.add_node(\"extract_events\", extract_events)\n", "graph.add_node(\"merge_results\", merge_results)\n", "\n", "graph.set_entry_point(\"extract_case_info\")\n", "graph.add_edge(\"extract_case_info\", \"extract_people\")\n", "graph.add_edge(\"extract_people\", \"extract_organizations\")\n", "graph.add_edge(\"extract_organizations\", \"extract_locations\")\n", "graph.add_edge(\"extract_locations\", \"extract_laws\")\n", "graph.add_edge(\"extract_laws\", \"extract_events\")\n", "graph.add_edge(\"extract_events\", \"merge_results\")\n", "graph.add_edge(\"merge_results\", END)\n", "\n", "workflow = graph.compile()"]}, {"cell_type": "code", "execution_count": 34, "id": "c22ce8b4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["RES: case=CaseInfo(case_id='TCDM,111,中簡,2601,********,1', case_year='111', judgment_year='2024', judgment_month='01', judgment_day='31', case_type='中簡', case_number='2601', title='詐欺等', judgment_type='substantive', verdict_items=[PenaltyItem(type='罰金', content='處罰金新臺幣8000元'), PenaltyItem(type='拘役', content='處拘役20日'), PenaltyItem(type='沒收', content='未扣案犯罪所得即價值新臺幣100元之汽油沒收')], judgment_date='2024-01-31', court='臺灣臺中地方法院', pdf_url='https://data.judicial.gov.tw/opendl/JDocFile/TCDM/111%2c%e4%b8%ad%e7%b0%a1%2c2601%2c********%2c1.pdf', prosecutors=None, judges=None, clerks=None) people=[Person(name='彭如鈴', uid='TCDM,111,中簡,2601,********,1_被告_1', role='被告', gender=None, is_anonymous=False, relationship_to_others=None), Person(name='林佳穎', uid='TCDM,111,中簡,2601,********,1_告訴人_1', role='告訴人', gender=None, is_anonymous=False, relationship_to_others=None), Person(name='洪進舟', uid='TCDM,111,中簡,2601,********,1_證人_1', role='證人', gender=None, is_anonymous=False, relationship_to_others='即進大輪業商行負責人'), Person(name='陳信郎', uid='TCDM,111,中簡,2601,********,1_檟察官_1', role='檟察官', gender=None, is_anonymous=False, relationship_to_others=None), Person(name='王曼寧', uid='TCDM,111,中簡,2601,********,1_法官_1', role='法官', gender=None, is_anonymous=False, relationship_to_others=None), Person(name='蔡昀潔', uid='TCDM,111,中簡,2601,********,1_書記官_1', role='書記官', gender=None, is_anonymous=False, relationship_to_others=None)] organizations=[Organization(name='中國信託商業銀行', org_type='銀行', related_persons=[]), Organization(name='進大輪業商行', org_type='商行', related_persons=['TCDM,111,中簡,2601,********,1_證人_1']), Organization(name='中油加油站向上路站', org_type='加油站', related_persons=[]), Organization(name='臺中市政府警察局第一分局', org_type='警察局', related_persons=[]), Organization(name='臺灣臺中地方檢察署', org_type='檢察署', related_persons=['TCDM,111,中簡,2601,********,1_檟察官_1']), Organization(name='臺灣臺中地方法院', org_type='法院', related_persons=['TCDM,111,中簡,2601,********,1_法官_1'])] locations=[Location(name='臺中市○區○○路0段00號「中油加油站向上路站」', type='加油站', address=None)] laws=[Law(law_name='中華民國刑法第337條', article='337', description='侵占遺失物罪'), Law(law_name='中華民國刑法第339條', article='339', description='詐欺取財罪')] events=[Event(event_type='侵占遺失物', event_id='TCDM,111,中簡,2601,********,1_ev1', participants=['TCDM,111,中簡,2601,********,1_被告_1'], target_persons=['TCDM,111,中簡,2601,********,1_告訴人_1'], location=None, date_time='2022-06-19', details=EventDetails(amount=None, bank='中國信託商業銀行', reason=None, method=None, confession=None, credibility=None, evidence_metrics=None)), Event(event_type='詐欺取財', event_id='TCDM,111,中簡,2601,********,1_ev2', participants=['TCDM,111,中簡,2601,********,1_被告_1'], target_persons=['TCDM,111,中簡,2601,********,1_告訴人_1'], location='臺中市○區○○路0段00號中油加油站向上路站', date_time='2022-06-19 04:36', details=EventDetails(amount=100, bank='中國信託商業銀行', reason='冒用林佳穎名義刷卡消費', method='小額消費免簽名', confession=None, credibility=None, evidence_metrics=None))]\n"]}], "source": ["result = await workflow.ainvoke({\"content\": doc.page_content})\n", "print(\"RES:\", result[\"classification\"])"]}, {"cell_type": "markdown", "id": "********", "metadata": {}, "source": ["## Classification轉換成Cypher"]}, {"cell_type": "code", "execution_count": 35, "id": "508fa5c3", "metadata": {}, "outputs": [], "source": ["def classification_to_cypher(classification) -> str:\n", "    \"\"\"\n", "    將 Classification 物件轉換成 Neo4j Cypher 查詢\n", "    使用分離式語句避免變數作用域問題\n", "    \"\"\"\n", "    queries = []\n", "    case = classification.case\n", "    case_id = case.case_id\n", "\n", "    # --- <PERSON> ---\n", "    verdict_items = [f\"{v.type}: {v.content}\" for v in case.verdict_items or []]\n", "    queries.append(f\"MERGE (c:Case {{case_id: '{case_id}'}});\")\n", "    queries.append(f\"MATCH (c:Case {{case_id: '{case_id}'}}) SET c += {{\" + \", \".join([\n", "        f\"case_year: '{case.case_year}'\",\n", "        f\"judgment_year: '{case.judgment_year}'\",\n", "        f\"judgment_month: '{case.judgment_month}'\",\n", "        f\"judgment_day: '{case.judgment_day}'\",\n", "        f\"case_type: '{case.case_type}'\",\n", "        f\"case_number: '{case.case_number}'\",\n", "        f\"title: '{case.title}'\",\n", "        f\"judgment_type: '{case.judgment_type}'\",\n", "        f\"judgment_date: '{case.judgment_date}'\",\n", "        f\"court: '{case.court}'\",\n", "        f\"pdf_url: '{case.pdf_url}'\",\n", "        f\"verdict_items: {verdict_items}\"\n", "    ]) + \"};\")\n", "\n", "    # --- <PERSON> Nodes ---\n", "    if classification.people:\n", "        for idx, p in enumerate(classification.people):\n", "            queries.append(f\"MERGE (p{idx}:Person {{uid: '{p.uid}'}});\")\n", "            queries.append(f\"MATCH (p:Person {{uid: '{p.uid}'}}) SET p += {{\" + \", \".join([\n", "                f\"name: '{p.name}'\",\n", "                f\"role: '{p.role}'\",\n", "                f\"gender: '{p.gender or ''}'\",\n", "                f\"relationship_to_others: '{p.relationship_to_others or ''}'\",\n", "                f\"is_anonymous: {p.is_anonymous}\"\n", "            ]) + \"};\")\n", "\n", "    # --- Organization Nodes ---\n", "    if classification.organizations:\n", "        for idx, o in enumerate(classification.organizations):\n", "            queries.append(f\"MERGE (o{idx}:Organization {{name: '{o.name}'}});\")\n", "            queries.append(f\"MATCH (o:Organization {{name: '{o.name}'}}) SET o.org_type = '{o.org_type}';\")\n", "            \n", "            # 建立組織關係\n", "            if o.related_persons:\n", "                for pid in o.related_persons:\n", "                    queries.append(f\"MATCH (o:Organization {{name: '{o.name}'}}) MATCH (p:Person {{uid: '{pid}'}}) MERGE (p)-[:BELONGS_TO]->(o);\")\n", "\n", "    # --- Location Nodes ---\n", "    if classification.locations:\n", "        for idx, l in enumerate(classification.locations):\n", "            queries.append(f\"MERGE (l{idx}:Location {{name: '{l.name}'}});\")\n", "            queries.append(f\"MATCH (l:Location {{name: '{l.name}'}}) SET l.type = '{l.type or ''}', l.address = '{l.address or ''}';\")\n", "\n", "    # --- Law Nodes and Relations ---\n", "    if classification.laws:\n", "        for idx, law in enumerate(classification.laws):\n", "            law_query = [\n", "                f\"MATCH (c:Case {{case_id: '{case_id}'}})\",\n", "                f\"MERGE (law{idx}:Law {{article: '{law.article}'}})\",\n", "                f\"SET law{idx}.law_name = '{law.law_name}', law{idx}.description = '{law.description}'\",\n", "                f\"MERGE (c)-[:CITES_LAW]->(law{idx});\"\n", "            ]\n", "            queries.append(\"\\n\".join(law_query))\n", "\n", "    # --- Event Nodes and Relations ---\n", "    if classification.events:\n", "        for idx, e in enumerate(classification.events):\n", "            # 創建事件節點\n", "            event_query = [\n", "                f\"MATCH (c:Case {{case_id: '{case_id}'}})\",\n", "                f\"MERGE (e{idx}:Event {{event_id: '{e.event_id}'}})\",\n", "                f\"SET e{idx}.event_type = '{e.event_type}', e{idx}.date_time = '{e.date_time}'\"\n", "            ]\n", "            \n", "            if e.details:\n", "                d = e.details.model_dump()\n", "                for k, v in d.items():\n", "                    if v is not None:\n", "                        v_str = f\"'{v}'\" if isinstance(v, str) else v\n", "                        event_query.append(f\"SET e{idx}.{k} = {v_str}\")\n", "            \n", "            event_query.append(f\"MERGE (c)-[:HAS_EVENT]->(e{idx});\")\n", "            queries.append(\"\\n\".join(event_query))\n", "            \n", "            # 連接參與者\n", "            if e.participants:\n", "                for pid in e.participants:\n", "                    participant_query = [\n", "                        f\"MATCH (e:Event {{event_id: '{e.event_id}'}})\",\n", "                        f\"MATCH (p:Person {{uid: '{pid}'}})\",\n", "                        f\"MERGE (e)-[:HAS_PARTICIPANT]->(p);\"\n", "                    ]\n", "                    queries.append(\"\\n\".join(participant_query))\n", "            \n", "            # 連接受害者\n", "            if e.target_persons:\n", "                for tid in e.target_persons:\n", "                    target_query = [\n", "                        f\"MATCH (e:Event {{event_id: '{e.event_id}'}})\",\n", "                        f\"MATCH (p:Person {{uid: '{tid}'}})\",\n", "                        f\"MERGE (e)-[:TARGET]->(p);\"\n", "                    ]\n", "                    queries.append(\"\\n\".join(target_query))\n", "            \n", "            # 連接地點\n", "            if e.location:\n", "                location_query = [\n", "                    f\"MATCH (e:Event {{event_id: '{e.event_id}'}})\",\n", "                    f\"MATCH (l:Location {{name: '{e.location}'}})\",\n", "                    f\"MERGE (e)-[:IN_LOCATION]->(l);\"\n", "                ]\n", "                queries.append(\"\\n\".join(location_query))\n", "\n", "    return \"\\n\\n\".join(queries)"]}, {"cell_type": "code", "execution_count": 36, "id": "01c181a8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["MERGE (c:Case {case_id: 'TCDM,111,中簡,2601,********,1'});\n", "\n", "MATCH (c:Case {case_id: 'TCDM,111,中簡,2601,********,1'}) SET c += {case_year: '111', judgment_year: '2024', judgment_month: '01', judgment_day: '31', case_type: '中簡', case_number: '2601', title: '詐欺等', judgment_type: 'substantive', judgment_date: '2024-01-31', court: '臺灣臺中地方法院', pdf_url: 'https://data.judicial.gov.tw/opendl/JDocFile/TCDM/111%2c%e4%b8%ad%e7%b0%a1%2c2601%2c********%2c1.pdf', verdict_items: ['罰金: 處罰金新臺幣8000元', '拘役: 處拘役20日', '沒收: 未扣案犯罪所得即價值新臺幣100元之汽油沒收']};\n", "\n", "MERGE (p0:Person {uid: 'TCDM,111,中簡,2601,********,1_被告_1'});\n", "\n", "MATCH (p:Person {uid: 'TCDM,111,中簡,2601,********,1_被告_1'}) SET p += {name: '彭如鈴', role: '被告', gender: '', relationship_to_others: '', is_anonymous: False};\n", "\n", "MERGE (p1:Person {uid: 'TCDM,111,中簡,2601,********,1_告訴人_1'});\n", "\n", "MATCH (p:Person {uid: 'TCDM,111,中簡,2601,********,1_告訴人_1'}) SET p += {name: '林佳穎', role: '告訴人', gender: '', relationship_to_others: '', is_anonymous: False};\n", "\n", "MERGE (p2:Person {uid: 'TCDM,111,中簡,2601,********,1_證人_1'});\n", "\n", "MATCH (p:Person {uid: 'TCDM,111,中簡,2601,********,1_證人_1'}) SET p += {name: '洪進舟', role: '證人', gender: '', relationship_to_others: '即進大輪業商行負責人', is_anonymous: False};\n", "\n", "MERGE (p3:Person {uid: 'TCDM,111,中簡,2601,********,1_檟察官_1'});\n", "\n", "MATCH (p:Person {uid: 'TCDM,111,中簡,2601,********,1_檟察官_1'}) SET p += {name: '陳信郎', role: '檟察官', gender: '', relationship_to_others: '', is_anonymous: False};\n", "\n", "MERGE (p4:Person {uid: 'TCDM,111,中簡,2601,********,1_法官_1'});\n", "\n", "MATCH (p:Person {uid: 'TCDM,111,中簡,2601,********,1_法官_1'}) SET p += {name: '王曼寧', role: '法官', gender: '', relationship_to_others: '', is_anonymous: False};\n", "\n", "MERGE (p5:Person {uid: 'TCDM,111,中簡,2601,********,1_書記官_1'});\n", "\n", "MATCH (p:Person {uid: 'TCDM,111,中簡,2601,********,1_書記官_1'}) SET p += {name: '蔡昀潔', role: '書記官', gender: '', relationship_to_others: '', is_anonymous: False};\n", "\n", "MERGE (o0:Organization {name: '中國信託商業銀行'});\n", "\n", "MATCH (o:Organization {name: '中國信託商業銀行'}) SET o.org_type = '銀行';\n", "\n", "MERGE (o1:Organization {name: '進大輪業商行'});\n", "\n", "MATCH (o:Organization {name: '進大輪業商行'}) SET o.org_type = '商行';\n", "\n", "MATCH (o:Organization {name: '進大輪業商行'}) MATCH (p:Person {uid: 'TCDM,111,中簡,2601,********,1_證人_1'}) MERGE (p)-[:BELONGS_TO]->(o);\n", "\n", "MERGE (o2:Organization {name: '中油加油站向上路站'});\n", "\n", "MATCH (o:Organization {name: '中油加油站向上路站'}) SET o.org_type = '加油站';\n", "\n", "MERGE (o3:Organization {name: '臺中市政府警察局第一分局'});\n", "\n", "MATCH (o:Organization {name: '臺中市政府警察局第一分局'}) SET o.org_type = '警察局';\n", "\n", "MERGE (o4:Organization {name: '臺灣臺中地方檢察署'});\n", "\n", "MATCH (o:Organization {name: '臺灣臺中地方檢察署'}) SET o.org_type = '檢察署';\n", "\n", "MATCH (o:Organization {name: '臺灣臺中地方檢察署'}) MATCH (p:Person {uid: 'TCDM,111,中簡,2601,********,1_檟察官_1'}) MERGE (p)-[:BELONGS_TO]->(o);\n", "\n", "MERGE (o5:Organization {name: '臺灣臺中地方法院'});\n", "\n", "MATCH (o:Organization {name: '臺灣臺中地方法院'}) SET o.org_type = '法院';\n", "\n", "MATCH (o:Organization {name: '臺灣臺中地方法院'}) MATCH (p:Person {uid: 'TCDM,111,中簡,2601,********,1_法官_1'}) MERGE (p)-[:BELONGS_TO]->(o);\n", "\n", "MERGE (l0:Location {name: '臺中市○區○○路0段00號「中油加油站向上路站」'});\n", "\n", "MATCH (l:Location {name: '臺中市○區○○路0段00號「中油加油站向上路站」'}) SET l.type = '加油站', l.address = '';\n", "\n", "MATCH (c:Case {case_id: 'TCDM,111,中簡,2601,********,1'})\n", "MERGE (law0:Law {article: '337'})\n", "SET law0.law_name = '中華民國刑法第337條', law0.description = '侵占遺失物罪'\n", "MERGE (c)-[:CITES_LAW]->(law0);\n", "\n", "MATCH (c:Case {case_id: 'TCDM,111,中簡,2601,********,1'})\n", "MERGE (law1:Law {article: '339'})\n", "SET law1.law_name = '中華民國刑法第339條', law1.description = '詐欺取財罪'\n", "MERGE (c)-[:CITES_LAW]->(law1);\n", "\n", "MATCH (c:Case {case_id: 'TCDM,111,中簡,2601,********,1'})\n", "MERGE (e0:Event {event_id: 'TCDM,111,中簡,2601,********,1_ev1'})\n", "SET e0.event_type = '侵占遺失物', e0.date_time = '2022-06-19'\n", "SET e0.bank = '中國信託商業銀行'\n", "MERGE (c)-[:HAS_EVENT]->(e0);\n", "\n", "MATCH (e:Event {event_id: 'TCDM,111,中簡,2601,********,1_ev1'})\n", "MATCH (p:Person {uid: 'TCDM,111,中簡,2601,********,1_被告_1'})\n", "MERGE (e)-[:HAS_PARTICIPANT]->(p);\n", "\n", "MATCH (e:Event {event_id: 'TCDM,111,中簡,2601,********,1_ev1'})\n", "MATCH (p:Person {uid: 'TCDM,111,中簡,2601,********,1_告訴人_1'})\n", "MERGE (e)-[:TARGET]->(p);\n", "\n", "MATCH (c:Case {case_id: 'TCDM,111,中簡,2601,********,1'})\n", "MERGE (e1:Event {event_id: 'TCDM,111,中簡,2601,********,1_ev2'})\n", "SET e1.event_type = '詐欺取財', e1.date_time = '2022-06-19 04:36'\n", "SET e1.amount = 100\n", "SET e1.bank = '中國信託商業銀行'\n", "SET e1.reason = '冒用林佳穎名義刷卡消費'\n", "SET e1.method = '小額消費免簽名'\n", "MERGE (c)-[:HAS_EVENT]->(e1);\n", "\n", "MATCH (e:Event {event_id: 'TCDM,111,中簡,2601,********,1_ev2'})\n", "MATCH (p:Person {uid: 'TCDM,111,中簡,2601,********,1_被告_1'})\n", "MERGE (e)-[:HAS_PARTICIPANT]->(p);\n", "\n", "MATCH (e:Event {event_id: 'TCDM,111,中簡,2601,********,1_ev2'})\n", "MATCH (p:Person {uid: 'TCDM,111,中簡,2601,********,1_告訴人_1'})\n", "MERGE (e)-[:TARGET]->(p);\n", "\n", "MATCH (e:Event {event_id: 'TCDM,111,中簡,2601,********,1_ev2'})\n", "MATCH (l:Location {name: '臺中市○區○○路0段00號中油加油站向上路站'})\n", "MERGE (e)-[:IN_LOCATION]->(l);\n"]}], "source": ["cypher = classification_to_cypher(result[\"classification\"])\n", "print(cypher)"]}], "metadata": {"kernelspec": {"display_name": "p312", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 5}