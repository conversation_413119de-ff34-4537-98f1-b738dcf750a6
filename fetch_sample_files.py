"""
判決書取樣
只取得500篇的資料並平均分部法院
字數少於10000字
"""
# import json
# import os
# import random
# import shutil

# source_dir = "./verdict_flat"
# target_dir = "./sample_500"
# os.makedirs(target_dir, exist_ok=True)

# targets = ["竊盜", "公共危險", "詐欺等", "過失傷害", "毒品危害防制條例"]
# courts = ["臺灣臺北地方法院", "臺灣臺中地方法院", "臺灣高雄地方法院", "臺灣臺東地方法院"]

# # 想要的篇數配置
# per_type_per_court = 25

# # 收集資料
# candidates = {t: {c: [] for c in courts} for t in targets}

# for fname in os.listdir(source_dir):
#     if not fname.endswith(".json"):
#         continue
#     fpath = os.path.join(source_dir, fname)
#     with open(fpath, encoding="utf-8") as f:
#         data = json.load(f)
#         title = data.get("JTITLE", "")
#         court = data.get("JCOURTNAME", "")
#         text_len = len(data.get("JFULL", ""))

#         # 篩選條件：案由 & 法院 & 字數
#         for t in targets:
#             if t in title and court in courts and text_len <= 10000:
#                 candidates[t][court].append(fpath)

# # 抽樣 & 複製
# for t in targets:
#     for c in courts:
#         pool = candidates[t][c]
#         if len(pool) < per_type_per_court:
#             print(f"⚠️ {t} {c} 可用篇數不足 {len(pool)}")
#             sample_files = pool  # 盡量取
#         else:
#             sample_files = random.sample(pool, per_type_per_court)

#         for path in sample_files:
#             newname = f"{t}_{c}_{os.path.basename(path)}"
#             shutil.copy(path, os.path.join(target_dir, newname))
#             print(f"✅ {t} {c} -> {newname}")


import json
import os
import random
import shutil
from collections import defaultdict

# 設定
source_dir = "./verdict_flat"
target_dir = "./sample_500"
os.makedirs(target_dir, exist_ok=True)

# 目標設定
targets = ["竊盜", "公共危險", "詐欺等", "過失傷害", "毒品危害防制條例"]
courts = ["臺灣臺北地方法院", "臺灣臺中地方法院", "臺灣高雄地方法院", "臺灣臺東地方法院"]
per_type_per_court = 25

# 初始化
candidates = {t: {c: [] for c in courts} for t in targets}
not_matched = defaultdict(int)

# 收集資料
for fname in os.listdir(source_dir):
    if not fname.endswith(".json"):
        continue
    path = os.path.join(source_dir, fname)
    try:
        with open(path, encoding="utf-8") as f:
            data = json.load(f)
        title = data.get("JTITLE", "")
        full = data.get("JFULL", "")
        text_len = len(full)

        # 嘗試從 JFULL 抽出法院名稱
        if not full:
            not_matched["空白JFULL"] += 1
            continue
        court_line = full.splitlines()[0].strip()
        matched_court = next((c for c in courts if c in court_line), None)
        if not matched_court:
            not_matched["無法辨識法院"] += 1
            continue

        # 判斷是否屬於目標類型
        matched_type = next((t for t in targets if t in title), None)
        if not matched_type:
            not_matched["案由不在範圍"] += 1
            continue

        if text_len > 10000:
            not_matched["超過字數"] += 1
            continue

        # 加入候選集
        candidates[matched_type][matched_court].append(path)

    except Exception as e:
        not_matched["讀檔失敗"] += 1
        print(f"❌ 讀檔錯誤: {fname} - {e}")

# 抽樣 & 複製
count = 0
for t in targets:
    for c in courts:
        pool = candidates[t][c]
        if not pool:
            print(f"⚠️ 無可用資料：{t} x {c}")
            continue
        selected = random.sample(pool, min(per_type_per_court, len(pool)))
        for path in selected:
            newname = f"{t}_{c}_{os.path.basename(path)}"
            shutil.copy(path, os.path.join(target_dir, newname))
            print(f"✅ 複製：{t} x {c} -> {newname}")
            count += 1

print("\n📊 統計報告")
print(f"✅ 總共複製：{count} 篇")
for reason, n in not_matched.items():
    print(f"❌ {reason}: {n}")