"""
分析類形
"""
import json
import os
from collections import Counter

counter = Counter()

for f in os.listdir("verdict_flat"):
    if not f.endswith(".json"):
        continue
    with open(os.path.join("verdict_flat", f), encoding="utf-8") as file:
        data = json.load(file)
        title = data.get("JTITLE", "").strip()
        if title:
            counter[title] += 1

for title, count in counter.most_common(20):
    print(f"{title}: {count}")
