import os
import json

lengths = []

for f in os.listdir("verdict_flat"):
    if not f.endswith(".json"):
        continue
    with open(os.path.join("verdict_flat", f), encoding="utf-8") as file:
        data = json.load(file)
        content = data.get("JFULL", "")
        lengths.append(len(content))

total = sum(lengths)
average = total / len(lengths)
max_len = max(lengths)
min_len = min(lengths)

print(f"總篇數：{len(lengths)}")
print(f"總字數：{total}")
print(f"平均字數：{average:.2f}")
print(f"最長篇：{max_len}")
print(f"最短篇：{min_len}")
