"""
判決書分析系統
從 Jupyter notebook 轉換而來的 Python 檔案
"""

# 判決書分析
# Load Json file
# 不使用JsonLoader..我發現自己轉成Document還比較知道過程發生了什麼，也方便自己加東加西

import json
from pathlib import Path
from pprint import pprint

from langchain_core.documents import Document


def load_judgment_data(file_path: str):
    """載入判決書 JSON 檔案"""
    file_path = Path(file_path)
    
    with file_path.open(encoding="utf-8") as f:
        data = json.load(f)
    
    pprint(data)
    
    # 加入自訂
    jdate = data.get("JDATE")
    jyear = jdate[:4]   # 年
    jmonth = jdate[4:6] # 月
    jday = jdate[6:]    # 日

    content_block = {
        "content": data.get("JFULL", ""),
        "metadata": {
            "title": data.get("JTITLE"),
            "date": data.get("JDATE"),
            "id": data.get("JID"),
            "no": data.get("JNO"),
            "year": data.get("JYEAR"),
            "case": data.get("JCASE"),
            "pdf_url": data.get("JPDF"),
            "judgment_year": jyear,
            "judgment_month": jmonth,
            "judgment_day": jday,
            "judgment_date": f"{jyear}-{jmonth}-{jday}",
        }
    }

    content_text = json.dumps(content_block, ensure_ascii=False)
    
    # 建立Document
    doc = Document(page_content=content_text, metadata={})
    return doc


# 定義要萃取的分類schema
from typing import List, Optional

from pydantic import BaseModel, Field


class Prosecutor(BaseModel):
    name: Optional[str] = Field(None, description="檢察官姓名，若未具名可為 None")
    is_anonymous: Optional[bool] = Field(None, description="是否為匿名檢察官，匿名的特性是最後兩個字為○○")


class Judge(BaseModel):
    name: Optional[str] = Field(None, description="法官姓名，若未具名可為 None")
    is_presiding: Optional[bool] = Field(None, description="是否為審判長（主審法官）,如果本案只有一個法官，則為 True")
    is_anonymous: Optional[bool] = Field(None, description="是否為匿名法官，匿名的特性是姓名最後兩個字為○○")


class Clerk(BaseModel):
    name: Optional[str] = Field(None, description="書記官姓名，若未具名可為 None")
    is_anonymous: Optional[bool] = Field(None, description="是否為匿名書記官，匿名的特性是最後姓名兩個字為○○")


class Person(BaseModel):
    """
    只取得案件中的人物資料，不取得法官、檢察官、書記官、聲請人等職務資料
    """
    name: str = Field(..., description="人物姓名或代稱")
    uid: Optional[str] = Field(None, description="人物的唯一識別碼，若有")
    role: str = Field(..., description="人物在案件中的角色，請使用自然語言描述，如『被告』、『證人』、『證人母親』")
    gender: Optional[str] = Field(None, description="人物性別（若可推論）")
    is_anonymous: Optional[bool] = Field(None, description="是否為匿名人物，匿名的特性是姓名最後兩個字為○○")
    relationship_to_others: Optional[str] = Field(None, description="與他人的關係描述，例如：『主嫌女友』")


class Organization(BaseModel):
    """
    只取得案件中提到的公司或組織，不包含檢察署、法院等
    """
    name: str = Field(..., description="組織名稱，例如保險公司、法院、醫院")
    org_type: str = Field(..., description="組織類型，請使用自然語言描述，如『民營保險公司』、『醫療機構』")
    related_persons: Optional[List[str]] = Field(None, description="與該組織有關聯的人物姓名清單")


class Location(BaseModel):
    name: str = Field(..., description="地點名稱，例如『澎湖縣203縣道3.75公里』")
    type: Optional[str] = Field(None, description="地點類型，如事故現場、公司地址等")
    address: Optional[str] = Field(None, description="完整地址（如有）")


class Law(BaseModel):
    law_name: str = Field(..., description="完整法條名稱，例如『刑法第185條之3第1項』")
    article: Optional[str] = Field(None, description="條號簡寫，例如『185-3-1』")
    description: Optional[str] = Field(None, description="法條摘要或罪名，例如『酒駕致人於死』")


class EventDetails(BaseModel):
    amount: Optional[int] = Field(None, description="涉案金額，若有金流")
    bank: Optional[str] = Field(None, description="涉及銀行名稱")
    reason: Optional[str] = Field(None, description="付款理由或詐騙藉口")
    method: Optional[str] = Field(None, description="事件方式，例如『潑汽油引燃』")
    confession: Optional[str] = Field(None, description="證人或被告的供述內容")
    credibility: Optional[str] = Field(None, description="供述可信度（如 high / low / unknown）")
    evidence_metrics: Optional[str] = Field(None, description="自由文字描述的數值或檢測結果，例如『酒測值為0.34mg/L』")


class Event(BaseModel):
    event_type: str = Field(..., description="事件類型，請使用自然語言描述，如『酒後駕車』、『縱火準備行為』")
    event_id: Optional[str] = Field(None, description="事件的唯一識別碼，若有")
    participants: List[str] = Field(..., description="參與事件的人物姓名清單")
    target_persons: Optional[List[str]] = Field(None, description="受害人或目標對象的姓名清單")
    location: Optional[str] = Field(None, description="事件發生地點的名稱")
    date_time: Optional[str] = Field(None, description="事件發生時間，建議使用 yyyy-mm-dd 或 yyyy-mm-dd hh:mm 格式")
    details: Optional[EventDetails] = Field(None, description="事件的細節描述")


class PenaltyItem(BaseModel):
    type: str = Field(..., description="處罰類型，例如：罰金、拘役、沒收、追徵、緩刑、免訴等")
    content: str = Field(..., description=(
        "簡明描述該項處罰的重點資訊，例如金額或天數。"
        "避免使用冗長句子，只保留數字與關鍵詞。"
        "例如：『罰金8000元』、『拘役20日』、『沒收汽油100元』")
    )


class CaseInfo(BaseModel):
    case_id: str = Field(..., description="完整案件 ID，例如 'TCDM,111,訴,2522,20240124,2'")
    case_year: Optional[str] = Field(None, description="案件年度 (中華民國紀年)")
    judgment_year: Optional[str] = Field(None, description="判決年度 (格式yyyy)")
    judgment_month: Optional[str] = Field(None, description="判決月份 (格式mm)")
    judgment_day: Optional[str] = Field(None, description="判決日期 (格式dd)")
    case_type: str = Field(..., description="案件類型代碼，如『訴』、『交訴』")
    case_number: Optional[str] = Field(None, description="案件流水號")
    title: Optional[str] = Field(None, description="案件標題，例如『違反商業會計法等』")
    judgment_type: str = Field(..., description="判決類型（如 substantive, procedural, other）")
    verdict_items: Optional[List[PenaltyItem]] = Field(None, description="主文中所有處罰項目的清單，每項包含類型與簡述，例如 [{'type': '罰金', 'content': '處罰金新臺幣8000元'}]")
    judgment_date: Optional[str] = Field(None, description="判決日期（格式 yyyy-mm-dd）")
    court: Optional[str] = Field(None, description="審理法院名稱")
    pdf_url: Optional[str] = Field(None, description="原始判決書 PDF 下載連結")
    prosecutors: Optional[List[Prosecutor]] = Field(None, description="參與本案的檢察官清單")
    judges: Optional[List[Judge]] = Field(None, description="參與本案的法官清單")
    clerks: Optional[Clerk] = Field(None, description="參與本案的書記官")


class Classification(BaseModel):
    case: CaseInfo = Field(..., description="案件的基本資料")
    people: List[Person] = Field(..., description="案件中的所有人物")
    organizations: Optional[List[Organization]] = Field(None, description="組織或公司相關資訊")
    locations: Optional[List[Location]] = Field(None, description="判決中提及的地點資訊")
    laws: Optional[List[Law]] = Field(None, description="所引用或涉及的法條")
    events: Optional[List[Event]] = Field(None, description="與本案有關的事件敘述與參與人")


# 初始化LLM
import getpass
import os


def setup_llm():
    """設定 OpenAI LLM"""
    if not os.environ.get("OPENAI_API_KEY"):
        os.environ["OPENAI_API_KEY"] = getpass.getpass("Enter API key for OpenAI: ")
    
    from langchain_openai import ChatOpenAI
    return ChatOpenAI(temperature=0, model="gpt-4o")


# 建立Prompt templates
from langchain_core.prompts import ChatPromptTemplate

basic_prompt_template = """
從以下判決書內容中，僅提取 {fields} 相關的資訊，並依照結構化定義回傳。
若找不到資料，請回傳空陣列或空值，不要發明或猜測。

判決書內容：
{content}
"""

# 統一產生prompt
def build_prompt(fields: str, content: str):
    return ChatPromptTemplate.from_template(basic_prompt_template).invoke({
        "fields": fields,
        "content": content,
    })


import sys
from collections import defaultdict
# 定義Node
from typing import Dict, List


# 包裝 List 用的 Schema..with_structured_output 不能直接吃List
class PeopleWrapper(BaseModel):
    people: List[Person] = Field(..., description="案件中的所有人物")


class OrgsWrapper(BaseModel):
    organizations: Optional[List[Organization]] = Field(None, description="組織或公司相關資訊")


class LocsWrapper(BaseModel):
    locations: Optional[List[Location]] = Field(None, description="判決中提及的地點資訊")


class LawsWrapper(BaseModel):
    laws: Optional[List[Law]] = Field(None, description="所引用或涉及的法條")


class EventsWrapper(BaseModel):
    events: Optional[List[Event]] = Field(None, description="與本案有關的事件敘述與參與人")


# 安全的提取欄資料位，給有Wapper的Schema拆解用
def safe_extract_field(result, field_name: str):
    if isinstance(result, tuple) and result[0] == field_name:
        return result[1]
    elif isinstance(result, BaseModel) and hasattr(result, field_name):
        return getattr(result, field_name)
    else:
        return []


# 取得案件基本資料
async def extract_case_info(state: Dict):
    llm = state.get("llm")
    prompt = build_prompt("case資訊 (CaseInfo)", state["content"])
    result = await llm.with_structured_output(CaseInfo).ainvoke(prompt)
    return {"case": result.model_dump(), "case_id": result.case_id}


# 取得人物資料
async def extract_people(state: Dict):
    llm = state.get("llm")
    prompt = build_prompt("人物清單 (People)", state["content"])
    result = await llm.with_structured_output(PeopleWrapper).ainvoke(prompt)
    people = safe_extract_field(result, "people")
    return {"people": [p.model_dump() for p in people]}


# 取得組織清單
async def extract_organizations(state: Dict):
    llm = state.get("llm")
    prompt = build_prompt("組織清單 (Organizations)", state["content"])
    result = await llm.with_structured_output(OrgsWrapper).ainvoke(prompt)
    organizations = safe_extract_field(result, "organizations")
    return {"organizations": [o.model_dump() for o in organizations]}


# 取得地點清單
async def extract_locations(state: Dict):
    llm = state.get("llm")
    prompt = build_prompt("地點清單 (Locations)", state["content"])
    result = await llm.with_structured_output(LocsWrapper).ainvoke(prompt)
    locations = safe_extract_field(result, "locations")
    return {"locations": [l.model_dump() for l in locations]}


# 取得法條清單
async def extract_laws(state: Dict):
    llm = state.get("llm")
    prompt = build_prompt("法條清單 (Laws)", state["content"])
    result = await llm.with_structured_output(LawsWrapper).ainvoke(prompt)
    laws = safe_extract_field(result, "laws")
    return {"laws": [l.model_dump() for l in laws]}


# 取得事件清單
async def extract_events(state: Dict):
    llm = state.get("llm")
    prompt = build_prompt("事件清單 (Events)", state["content"])
    result = await llm.with_structured_output(EventsWrapper).ainvoke(prompt)
    events = safe_extract_field(result, "events")
    return {"events": [e.model_dump() for e in events]}


# 整合所有結果  
async def merge_results(state: Dict):
    case = state["case"]
    case_id = case["case_id"]

    # --------- 統一補上 role / uid 給特殊角色（檢察官、法官、書記官）---------
    role_defaults = {
        "prosecutors": "檢察官",
        "judges": "法官",
        "clerks": "書記官"
    }

    special_people = []

    for key, default_role in role_defaults.items():
        value = case.get(key)
        if isinstance(value, list):
            for idx, item in enumerate(value, 1):
                person = dict(item)
                role = person.get("role", default_role)
                person["role"] = role
                special_people.append(person)
        elif isinstance(value, dict):  # e.g. clerks
            person = dict(value)
            role = person.get("role", default_role)
            person["role"] = role
            special_people.append(person)

    # --------- 合併所有人物並補上 uid ---------
    people = state.get("people", [])
    people = [dict(p) for p in people]  # 確保是可變 dict
    all_people = people + special_people
    role_counter = defaultdict(int)
    
    for person in all_people:
        # 修正可能的角色名稱錯字
        role = person["role"].replace(" ", "")
        person["role"] = role  # 更新角色名稱
        role_counter[role] += 1
        person["uid"] = f"{case_id}_{role}_{role_counter[role]}"

    # --------- 移除 case 中的特殊角色欄位，避免重複 ---------
    case.pop("prosecutors", None)
    case.pop("judges", None)
    case.pop("clerks", None)

    # --------- 建立 name → uid 對應表（只限非匿名） ---------
    name_to_uid = {
        p["name"]: p["uid"]
        for p in all_people
        if p.get("name") and not p.get("is_anonymous", False)
    }

    # --------- 補上 event_id，並將 name 轉為 uid ---------
    events = state.get("events", [])
    events = [dict(ev) for ev in events]  # 確保是可變 dict
    for idx, ev in enumerate(events, 1):
        if not ev.get("event_id"):
            ev["event_id"] = f"{case_id}_ev{idx}"

        ev["participants"] = [
            name_to_uid.get(name, name) for name in ev.get("participants", [])
        ]
        ev["target_persons"] = [
            name_to_uid.get(name, name) for name in ev.get("target_persons", [])
        ]

    # --------- 組織裡的 related_persons 也轉成 uid ---------
    organizations = state.get("organizations", [])
    organizations = [dict(o) for o in organizations]
    for org in organizations:
        org["related_persons"] = [
            name_to_uid.get(name, name)
            for name in org.get("related_persons", []) or []
        ]

    # --------- verdict_items 轉為 PenaltyItem 型別 ---------
    verdict_items = case.get("verdict_items")
    if verdict_items and isinstance(verdict_items, list) and all(isinstance(v, str) for v in verdict_items):
        case["verdict_items"] = [
            PenaltyItem(type=v.split(":")[0].strip(), content=v.split(":", 1)[1].strip())
            for v in verdict_items if ":" in v
        ]

    # --------- 整合成 Classification ---------
    merged = {
        "case": case,
        "people": all_people,
        "organizations": organizations,
        "locations": state.get("locations", []),
        "laws": state.get("laws", []),
        "events": events,
    }

    return {"classification": Classification(**merged)}


# Compile graph
from typing import List, TypedDict


class JudgmentState(TypedDict, total=False):
    content: str
    case: dict
    people: List[dict]
    organizations: List[dict]
    locations: List[dict]
    laws: List[dict]
    events: List[dict]
    classification: dict
    llm: object


def create_workflow():
    """建立工作流程"""
    from langgraph.graph import END, StateGraph
    
    graph = StateGraph(JudgmentState)
    
    graph.add_node("extract_case_info", extract_case_info)
    graph.add_node("extract_people", extract_people)
    graph.add_node("extract_organizations", extract_organizations)
    graph.add_node("extract_locations", extract_locations)
    graph.add_node("extract_laws", extract_laws)
    graph.add_node("extract_events", extract_events)
    graph.add_node("merge_results", merge_results)
    
    graph.set_entry_point("extract_case_info")
    graph.add_edge("extract_case_info", "extract_people")
    graph.add_edge("extract_people", "extract_organizations")
    graph.add_edge("extract_organizations", "extract_locations")
    graph.add_edge("extract_locations", "extract_laws")
    graph.add_edge("extract_laws", "extract_events")
    graph.add_edge("extract_events", "merge_results")
    graph.add_edge("merge_results", END)
    
    return graph.compile()


# Classification轉換成Cypher
def classification_to_cypher(classification: Classification) -> str:
    """
    將 Classification 物件轉換成 Neo4j Cypher 查詢
    使用分離式語句避免變數作用域問題
    """
    queries = []
    case = classification.case
    case_id = case.case_id

    # --- Case Node ---
    verdict_items = [f"{v.type}: {v.content}" for v in case.verdict_items or []]
    queries.append(f"MERGE (c:Case {{case_id: '{case_id}'}});")
    queries.append(f"MATCH (c:Case {{case_id: '{case_id}'}}) SET c += {{" + ", ".join([
        f"case_year: '{case.case_year}'",
        f"judgment_year: '{case.judgment_year}'",
        f"judgment_month: '{case.judgment_month}'",
        f"judgment_day: '{case.judgment_day}'",
        f"case_type: '{case.case_type}'",
        f"case_number: '{case.case_number}'",
        f"title: '{case.title}'",
        f"judgment_type: '{case.judgment_type}'",
        f"judgment_date: '{case.judgment_date}'",
        f"court: '{case.court}'",
        f"pdf_url: '{case.pdf_url}'",
        f"verdict_items: {verdict_items}"
    ]) + "};")

    # --- People Nodes ---
    if classification.people:
        for idx, p in enumerate(classification.people):
            queries.append(f"MERGE (p{idx}:Person {{uid: '{p.uid}'}});")
            queries.append(f"MATCH (p:Person {{uid: '{p.uid}'}}) SET p += {{" + ", ".join([
                f"name: '{p.name}'",
                f"role: '{p.role}'",
                f"gender: '{p.gender or ''}'",
                f"relationship_to_others: '{p.relationship_to_others or ''}'",
                f"is_anonymous: {p.is_anonymous}"
            ]) + "};")

    # --- Organization Nodes ---
    if classification.organizations:
        for idx, o in enumerate(classification.organizations):
            queries.append(f"MERGE (o{idx}:Organization {{name: '{o.name}'}});")
            queries.append(f"MATCH (o:Organization {{name: '{o.name}'}}) SET o.org_type = '{o.org_type}';")
            
            # 建立組織關係
            if o.related_persons:
                for pid in o.related_persons:
                    queries.append(f"MATCH (o:Organization {{name: '{o.name}'}}) MATCH (p:Person {{uid: '{pid}'}}) MERGE (p)-[:BELONGS_TO]->(o);")

    # --- Location Nodes ---
    if classification.locations:
        for idx, l in enumerate(classification.locations):
            queries.append(f"MERGE (l{idx}:Location {{name: '{l.name}'}});")
            queries.append(f"MATCH (l:Location {{name: '{l.name}'}}) SET l.type = '{l.type or ''}', l.address = '{l.address or ''}';")

    # --- Law Nodes and Relations ---
    if classification.laws:
        for idx, law in enumerate(classification.laws):
            law_query = [
                f"MATCH (c:Case {{case_id: '{case_id}'}})",
                f"MERGE (law{idx}:Law {{article: '{law.article}'}})",
                f"SET law{idx}.law_name = '{law.law_name}', law{idx}.description = '{law.description}'",
                f"MERGE (c)-[:CITES_LAW]->(law{idx});"
            ]
            queries.append("\n".join(law_query))

    # --- Event Nodes and Relations ---
    if classification.events:
        for idx, e in enumerate(classification.events):
            # 創建事件節點
            event_query = [
                f"MATCH (c:Case {{case_id: '{case_id}'}})",
                f"MERGE (e{idx}:Event {{event_id: '{e.event_id}'}})",
                f"SET e{idx}.event_type = '{e.event_type}', e{idx}.date_time = '{e.date_time}'"
            ]
            
            if e.details:
                d = e.details.model_dump()
                for k, v in d.items():
                    if v is not None:
                        v_str = f"'{v}'" if isinstance(v, str) else v
                        event_query.append(f"SET e{idx}.{k} = {v_str}")
            
            event_query.append(f"MERGE (c)-[:HAS_EVENT]->(e{idx});")
            queries.append("\n".join(event_query))
            
            # 連接參與者
            if e.participants:
                for pid in e.participants:
                    participant_query = [
                        f"MATCH (e:Event {{event_id: '{e.event_id}'}})",
                        f"MATCH (p:Person {{uid: '{pid}'}})",
                        f"MERGE (e)-[:HAS_PARTICIPANT]->(p);"
                    ]
                    queries.append("\n".join(participant_query))
            
            # 連接受害者
            if e.target_persons:
                for tid in e.target_persons:
                    target_query = [
                        f"MATCH (e:Event {{event_id: '{e.event_id}'}})",
                        f"MATCH (p:Person {{uid: '{tid}'}})",
                        f"MERGE (e)-[:TARGET]->(p);"
                    ]
                    queries.append("\n".join(target_query))
            
            # 連接地點
            if e.location:
                location_query = [
                    f"MATCH (e:Event {{event_id: '{e.event_id}'}})",
                    f"MATCH (l:Location {{name: '{e.location}'}})",
                    f"MERGE (e)-[:IN_LOCATION]->(l);"
                ]
                queries.append("\n".join(location_query))

    return "\n\n".join(queries)


# 主要執行函數
async def process_judgment(file_path: str):
    """處理判決書的主要函數"""
    # 載入判決書資料
    doc = load_judgment_data(file_path)
    
    # 設定 LLM
    llm = setup_llm()
    
    # 建立工作流程
    workflow = create_workflow()
    
    # 執行分析
    result = await workflow.ainvoke({
        "content": doc.page_content,
        "llm": llm
    })
    
    print("分析結果:", result["classification"])
    return result["classification"]


if __name__ == "__main__":
    import asyncio

    # 讀取 sample_500 資料夾所有 json 檔案，逐一分析
    from pathlib import Path

    sample_dir = Path("sample_500")
    json_files = list(sample_dir.glob("*.json"))

    for file_path in json_files:
        print(f"分析檔案: {file_path}")
        result = asyncio.run(process_judgment(str(file_path)))
        cypher_query = classification_to_cypher(result)
        print("\nCypher 查詢:")
        print(cypher_query)