# Verdict 法律知識圖譜聊天查詢系統 - 完整架構設計

## 專案概述

本系統是一個基於法院判決書的智能查詢系統，結合大語言模型（OpenAI GPT-4）與知識圖譜技術，提供自然語言查詢界面，並以視覺化知識圖譜的方式呈現查詢結果。

### 核心特色

- **自然語言查詢**：支援中文自然語言查詢法律資料
- **知識圖譜視覺化**：以互動式圖形展示複雜的法律關係
- **AI 驅動**：使用 GPT-4 理解查詢意圖並生成 Cypher 查詢
- **實時響應**：WebSocket 支援即時查詢進度反饋
- **語義豐富**：基於 Neo4j 構建的結構化法律知識庫

## 1. 系統架構概覽

```mermaid
graph TB
    subgraph "前端層 (Frontend)"
        A1[Vue.js 3 聊天界面]
        A2[D3.js 知識圖譜視覺化]
        A3[Element Plus UI組件]
    end

    subgraph "後端層 (Backend)"
        B1[FastAPI REST API]
        B2[查詢處理服務]
        B3[圖譜生成服務]
        B4[WebSocket 即時通信]
    end

    subgraph "AI處理層 (AI Processing)"
        C1[OpenAI GPT-4]
        C2[自然語言→Cypher轉換]
        C3[回答生成引擎]
    end

    subgraph "資料層 (Data Layer)"
        D1[Neo4j 知識圖譜]
        D2[已有的判決書分析系統]
        D3[Cypher查詢引擎]
    end

    A1 --> B1
    A2 --> B3
    B1 --> C1
    B2 --> C2
    B3 --> D1
    C2 --> D3
    D2 --> D1

    style A1 fill:#e1f5fe
    style B1 fill:#f3e5f5
    style C1 fill:#fff3e0
    style D1 fill:#e8f5e8
```

## 2. 技術選型詳細分析

### 2.1 前端技術棧

#### 核心框架

- **Vue.js 3** (Composition API)
  - 理由：輕量、響應式、易學習、完整生態系統
  - 版本：3.4+
  - 特點：組合式 API、更好的效能、簡潔的 JavaScript 開發

#### UI 框架

- **Element Plus**
  - 理由：成熟的 Vue 3 UI 庫，中文文檔完善
  - 組件：聊天框、按鈕、表單、載入動畫等
  - 主題：可自定義符合法律專業的視覺風格

#### 圖形化視覺化

- **D3.js v7**
  - 理由：最強大且靈活的圖形視覺化庫
  - 功能：知識圖譜渲染、節點互動、動畫效果
  - 優勢：完全客製化、高效能、豐富的佈局演算法

#### 狀態管理與路由

- **Pinia**：現代化的 Vue 狀態管理庫
- **Vue Router**：官方路由管理器
- **Axios**：HTTP 請求處理庫

### 2.2 後端技術棧

#### 核心框架

- **FastAPI**
  - 理由：高效能、自動 API 文檔生成、優秀的 Python 支援
  - 版本：0.104+
  - 特點：基於 Pydantic 的自動資料驗證、原生 async/await 支援

#### 資料庫與 AI 整合

- **Neo4j Python Driver**：官方 Neo4j 驅動程式
- **OpenAI Python SDK**：GPT-4 模型整合
- **Pydantic**：資料模型驗證（已有基礎）

#### 部署與服務

- **Uvicorn**：高效能 ASGI 應用伺服器
- **WebSocket**：即時通信支援
- **Docker**：容器化部署

### 2.3 資料庫架構

- **Neo4j**：圖形資料庫
- **已有的判決書分析系統**：基於 LangGraph 的資料萃取流程
- **豐富的 Schema**：Person、Case、Organization、Location、Law、Event 等實體類型

## 3. 資料模型設計

### 3.1 知識圖譜節點類型

基於現有的 Pydantic 模型，我們定義了以下節點類型：

```python
# 核心實體類型
NODE_TYPES = {
    "Person": {
        "properties": ["name", "uid", "role", "gender", "is_anonymous", "relationship_to_others"],
        "color": "#4A90E2",
        "shape": "circle",
        "icon": "👤"
    },
    "Case": {
        "properties": ["case_id", "title", "judgment_type", "court", "judgment_date"],
        "color": "#E94B4B",
        "shape": "rectangle",
        "icon": "📄"
    },
    "Organization": {
        "properties": ["name", "org_type"],
        "color": "#50C878",
        "shape": "diamond",
        "icon": "🏢"
    },
    "Location": {
        "properties": ["name", "type", "address"],
        "color": "#FF8C00",
        "shape": "hexagon",
        "icon": "📍"
    },
    "Law": {
        "properties": ["law_name", "article", "description"],
        "color": "#9370DB",
        "shape": "triangle",
        "icon": "⚖️"
    },
    "Event": {
        "properties": ["event_type", "event_id", "date_time"],
        "color": "#FFD700",
        "shape": "ellipse",
        "icon": "⚡"
    }
}
```

### 3.2 關係類型定義

```python
RELATIONSHIP_TYPES = {
    "HAS_PARTICIPANT": {
        "label": "參與",
        "color": "#666666",
        "width": 2,
        "style": "solid"
    },
    "CITES_LAW": {
        "label": "引用法條",
        "color": "#9370DB",
        "width": 3,
        "style": "dashed"
    },
    "BELONGS_TO": {
        "label": "隸屬於",
        "color": "#50C878",
        "width": 2,
        "style": "solid"
    },
    "TARGET": {
        "label": "受害者",
        "color": "#E94B4B",
        "width": 2,
        "style": "solid"
    },
    "IN_LOCATION": {
        "label": "發生地點",
        "color": "#FF8C00",
        "width": 1,
        "style": "dotted"
    },
    "HAS_EVENT": {
        "label": "相關事件",
        "color": "#FFD700",
        "width": 2,
        "style": "solid"
    }
}
```

## 4. 系統架構設計

### 4.1 前端架構

```
frontend/
├── src/
│   ├── components/
│   │   ├── ChatInterface/
│   │   │   ├── ChatWindow.vue          # 主聊天窗口
│   │   │   ├── MessageList.vue         # 消息歷史列表
│   │   │   ├── MessageItem.vue         # 單條消息組件
│   │   │   ├── InputBox.vue            # 查詢輸入框
│   │   │   └── TypingIndicator.vue     # 輸入狀態指示器
│   │   ├── KnowledgeGraph/
│   │   │   ├── GraphContainer.vue      # 圖譜主容器
│   │   │   ├── GraphRenderer.vue       # D3.js圖譜渲染器
│   │   │   ├── NodeTooltip.vue         # 節點資訊提示
│   │   │   ├── GraphControls.vue       # 圖譜操作控制器
│   │   │   └── GraphLegend.vue         # 圖譜圖例
│   │   ├── Layout/
│   │   │   ├── MainLayout.vue          # 主要佈局
│   │   │   ├── Sidebar.vue             # 側邊欄
│   │   │   └── Header.vue              # 頂部導航
│   │   └── Common/
│   │       ├── LoadingSpinner.vue      # 載入動畫
│   │       ├── ErrorMessage.vue        # 錯誤訊息
│   │       └── EmptyState.vue          # 空狀態顯示
│   ├── services/
│   │   ├── api.ts                      # HTTP API請求服務
│   │   ├── websocket.ts                # WebSocket連接服務
│   │   ├── graphRenderer.ts            # 圖譜渲染服務
│   │   └── queryParser.ts              # 查詢解析服務
│   ├── stores/
│   │   ├── chat.ts                     # 聊天狀態管理
│   │   ├── graph.ts                    # 圖譜狀態管理
│   │   └── user.ts                     # 用戶狀態管理
│   ├── types/
│   │   ├── chat.ts                     # 聊天相關介面定義
│   │   ├── graph.ts                    # 圖譜相關介面定義
│   │   └── api.ts                      # API響應介面定義
│   ├── utils/
│   │   ├── formatters.ts               # 資料格式化工具
│   │   ├── validators.ts               # 資料驗證工具
│   │   └── constants.ts                # 常數定義
│   ├── styles/
│   │   ├── main.scss                   # 主要樣式
│   │   ├── components.scss             # 組件樣式
│   │   └── graph.scss                  # 圖譜專用樣式
│   ├── router/
│   │   └── index.ts                    # 路由配置
│   ├── App.vue                         # 根組件
│   └── main.ts                         # 應用入口
├── public/
│   └── index.html
├── package.json
├── vite.config.ts
└── tsconfig.json
```

### 4.2 後端架構

```
backend/
├── app/
│   ├── api/
│   │   ├── deps.py                     # 依賴注入
│   │   ├── routes/
│   │   │   ├── __init__.py
│   │   │   ├── chat.py                 # 聊天API路由
│   │   │   ├── graph.py                # 圖譜API路由
│   │   │   ├── query.py                # 查詢API路由
│   │   │   └── health.py               # 健康檢查路由
│   │   └── websocket.py                # WebSocket路由
│   ├── core/
│   │   ├── __init__.py
│   │   ├── config.py                   # 配置管理
│   │   ├── database.py                 # Neo4j資料庫連接
│   │   ├── security.py                 # 安全相關設定
│   │   └── logging.py                  # 日誌配置
│   ├── services/
│   │   ├── __init__.py
│   │   ├── chat_service.py             # 聊天邏輯服務
│   │   ├── query_service.py            # 查詢處理服務
│   │   ├── graph_service.py            # 圖譜操作服務
│   │   ├── ai_service.py               # AI整合服務
│   │   └── cache_service.py            # 快取服務
│   ├── models/
│   │   ├── __init__.py
│   │   ├── chat_models.py              # 聊天資料模型
│   │   ├── graph_models.py             # 圖譜資料模型
│   │   ├── query_models.py             # 查詢資料模型
│   │   └── response_models.py          # API響應模型
│   ├── utils/
│   │   ├── __init__.py
│   │   ├── cypher_generator.py         # Cypher查詢生成器
│   │   ├── response_formatter.py       # 響應格式化工具
│   │   ├── error_handler.py            # 錯誤處理工具
│   │   └── text_processor.py           # 文本處理工具
│   ├── schemas/
│   │   ├── __init__.py
│   │   └── verdict_schema.py           # 現有的判決書Schema
│   └── main.py                         # FastAPI應用入口
├── tests/
│   ├── __init__.py
│   ├── test_api.py                     # API測試
│   ├── test_services.py                # 服務測試
│   └── test_utils.py                   # 工具測試
├── requirements.txt
├── Dockerfile
└── docker-compose.yml
```

## 5. API 設計規格

### 5.1 RESTful API 端點

#### 聊天相關 API

```python
# POST /api/v1/chat/message
# 發送聊天消息並獲取回應
{
    "message": "檢察官王XX處理過哪些詐欺案件？",
    "session_id": "optional-session-uuid"
}

# Response
{
    "message_id": "uuid",
    "session_id": "uuid",
    "timestamp": "2024-01-01T00:00:00Z",
    "query_analysis": {
        "intent": "person_cases_query",
        "entities": ["王XX", "詐欺案件"],
        "confidence": 0.95
    },
    "cypher_query": "MATCH (p:Person {name: '王XX'})-[:HAS_PARTICIPANT]-(c:Case) WHERE c.title CONTAINS '詐欺' RETURN p, c",
    "result_data": {
        "nodes": [...],
        "relationships": [...],
        "summary": {
            "total_cases": 5,
            "case_types": ["詐欺", "侵占"],
            "date_range": ["2022-01-01", "2024-01-01"]
        }
    },
    "ai_response": "根據查詢結果，檢察官王XX共處理了5件詐欺相關案件，包括純詐欺案件3件和詐欺侵占案件2件。這些案件的審理時間跨度為2022年至2024年。",
    "graph_data": {
        "nodes": [
            {
                "id": "person_wang_xx",
                "type": "Person",
                "properties": {"name": "王XX", "role": "檢察官"},
                "display": {"x": 100, "y": 100, "size": 20}
            }
        ],
        "edges": [
            {
                "source": "person_wang_xx",
                "target": "case_123",
                "type": "HAS_PARTICIPANT",
                "properties": {"role": "檢察官"}
            }
        ]
    }
}
```

#### 圖譜相關 API

```python
# GET /api/v1/graph/node/{node_id}
# 獲取特定節點的詳細資訊
{
    "node_id": "person_wang_xx",
    "node_type": "Person",
    "properties": {
        "name": "王XX",
        "role": "檢察官",
        "uid": "prosecutor_wang_xx_001"
    },
    "relationships": [
        {
            "type": "HAS_PARTICIPANT",
            "target_node": "case_123",
            "target_type": "Case",
            "properties": {}
        }
    ],
    "statistics": {
        "total_cases": 15,
        "case_types": {"詐欺": 5, "侵占": 3, "其他": 7},
        "success_rate": 0.87
    }
}

# POST /api/v1/graph/expand
# 擴展圖譜，獲取相關節點
{
    "center_node": "person_wang_xx",
    "expand_types": ["HAS_PARTICIPANT", "CITES_LAW"],
    "max_depth": 2,
    "limit": 50
}

# Response
{
    "expanded_nodes": [...],
    "new_relationships": [...],
    "expansion_summary": {
        "nodes_added": 12,
        "relationships_added": 23,
        "expansion_depth": 2
    }
}
```

#### 查詢相關 API

```python
# POST /api/v1/query/analyze
# 分析自然語言查詢
{
    "query": "找出所有詐欺案件中涉及銀行的案例",
    "context": {
        "previous_queries": [],
        "current_graph_focus": "case_types"
    }
}

# Response
{
    "query_intent": "case_organization_query",
    "extracted_entities": [
        {"entity": "詐欺案件", "type": "case_type"},
        {"entity": "銀行", "type": "organization_type"}
    ],
    "suggested_cypher": "MATCH (c:Case)-[:HAS_PARTICIPANT]-(e:Event)-[:INVOLVES]-(o:Organization) WHERE c.title CONTAINS '詐欺' AND o.org_type = '銀行' RETURN c, e, o",
    "explanation": "此查詢將找出所有詐欺案件中涉及銀行組織的案例，包括案件基本資訊、相關事件和涉及的銀行組織。",
    "estimated_results": 23,
    "confidence": 0.92
}

# POST /api/v1/query/execute
# 執行Cypher查詢
{
    "cypher": "MATCH (c:Case) WHERE c.title CONTAINS '詐欺' RETURN c LIMIT 10",
    "parameters": {},
    "format": "graph" // 或 "table"
}
```

### 5.2 WebSocket 通信協議

```python
# WebSocket端點: /ws/chat/{session_id}

# 客戶端發送查詢
{
    "type": "query_start",
    "data": {
        "message": "檢察官王XX處理過哪些案件？",
        "timestamp": "2024-01-01T00:00:00Z"
    }
}

# 服務端回應 - 查詢進度
{
    "type": "query_progress",
    "data": {
        "step": "analyzing_query",
        "progress": 20,
        "message": "正在分析查詢意圖...",
        "timestamp": "2024-01-01T00:00:01Z"
    }
}

{
    "type": "query_progress",
    "data": {
        "step": "generating_cypher",
        "progress": 50,
        "message": "正在生成Cypher查詢...",
        "timestamp": "2024-01-01T00:00:02Z"
    }
}

{
    "type": "query_progress",
    "data": {
        "step": "executing_query",
        "progress": 80,
        "message": "正在執行資料庫查詢...",
        "timestamp": "2024-01-01T00:00:03Z"
    }
}

# 服務端回應 - 查詢完成
{
    "type": "query_complete",
    "data": {
        "message_id": "uuid",
        "result": {
            "nodes": [...],
            "relationships": [...],
            "summary": {...}
        },
        "ai_response": "根據查詢結果...",
        "graph_data": {...},
        "timestamp": "2024-01-01T00:00:05Z"
    }
}

# 錯誤處理
{
    "type": "query_error",
    "data": {
        "error_code": "CYPHER_SYNTAX_ERROR",
        "error_message": "查詢語法錯誤",
        "details": "...",
        "timestamp": "2024-01-01T00:00:04Z"
    }
}
```

## 6. 自然語言查詢處理

### 6.1 查詢意圖分類

```python
QUERY_INTENTS = {
    "person_cases": {
        "patterns": [
            "檢察官 {name} 處理過哪些案件",
            "法官 {name} 審理過的案件",
            "{name} 參與的案件有哪些"
        ],
        "entities": ["person_name", "case_type"],
        "cypher_template": """
        MATCH (p:Person {{name: '{name}'}})-[:HAS_PARTICIPANT]-(c:Case)
        WHERE c.title CONTAINS '{case_type}'
        RETURN p, c, collect(distinct c.title) as cases
        ORDER BY c.judgment_date DESC
        """
    },
    "case_law": {
        "patterns": [
            "詐欺案件涉及哪些法條",
            "{case_type} 案件的相關法條",
            "法條 {law_article} 相關案件"
        ],
        "entities": ["case_type", "law_article"],
        "cypher_template": """
        MATCH (c:Case)-[:CITES_LAW]-(l:Law)
        WHERE c.title CONTAINS '{case_type}'
        RETURN c, l, collect(l.law_name) as laws
        ORDER BY count(l) DESC
        """
    },
    "organization_cases": {
        "patterns": [
            "涉及 {org_name} 的案件",
            "{org_type} 相關的案件有哪些",
            "哪些案件涉及銀行"
        ],
        "entities": ["org_name", "org_type"],
        "cypher_template": """
        MATCH (o:Organization)-[:BELONGS_TO]-(p:Person)-[:HAS_PARTICIPANT]-(c:Case)
        WHERE o.name CONTAINS '{org_name}' OR o.org_type CONTAINS '{org_type}'
        RETURN o, p, c
        ORDER BY c.judgment_date DESC
        """
    },
    "temporal_analysis": {
        "patterns": [
            "近三年的詐欺案件趨勢",
            "{year} 年的案件統計",
            "案件數量變化趨勢"
        ],
        "entities": ["time_period", "case_type"],
        "cypher_template": """
        MATCH (c:Case)
        WHERE c.judgment_year >= '{start_year}' AND c.judgment_year <= '{end_year}'
        AND c.title CONTAINS '{case_type}'
        RETURN c.judgment_year as year, count(c) as case_count
        ORDER BY year
        """
    }
}
```

### 6.2 AI 查詢生成服務

```python
from typing import Dict, Any
from pydantic import BaseModel
from openai import AsyncOpenAI

class QueryAnalysis(BaseModel):
    intent: str
    entities: Dict[str, str]
    confidence: float
    cypher_query: str
    explanation: str

class AIQueryService:
    def __init__(self, openai_api_key: str):
        self.client = AsyncOpenAI(api_key=openai_api_key)
        self.schema = self._load_neo4j_schema()

    async def analyze_query(self, natural_query: str) -> QueryAnalysis:
        """分析自然語言查詢並生成Cypher"""

        system_prompt = f"""
        你是一個專業的Neo4j查詢生成器，專門處理台灣法院判決書的資料查詢。

        資料庫Schema：
        {self.schema}

        請根據用戶的自然語言查詢，生成對應的Cypher查詢語句。

        回應格式：
        {{
            "intent": "查詢意圖類型",
            "entities": {{"實體類型": "實體值"}},
            "confidence": 0.95,
            "cypher_query": "MATCH ...",
            "explanation": "查詢說明"
        }}
        """

        user_prompt = f"用戶查詢：{natural_query}"

        response = await self.client.chat.completions.create(
            model="gpt-4o",
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ],
            response_format={"type": "json_object"},
            temperature=0.1
        )

        result = json.loads(response.choices[0].message.content)
        return QueryAnalysis(**result)

    def _load_neo4j_schema(self) -> str:
        """載入Neo4j資料庫Schema"""
        return """
        節點類型：
        - Person: 人物 (name, role, uid, gender, is_anonymous)
        - Case: 案件 (case_id, title, judgment_type, court, judgment_date)
        - Organization: 組織 (name, org_type)
        - Location: 地點 (name, type, address)
        - Law: 法條 (law_name, article, description)
        - Event: 事件 (event_type, event_id, date_time)

        關係類型：
        - HAS_PARTICIPANT: 參與案件
        - CITES_LAW: 引用法條
        - BELONGS_TO: 隸屬組織
        - TARGET: 受害者
        - IN_LOCATION: 發生地點
        - HAS_EVENT: 相關事件
        """
```

## 7. 知識圖譜視覺化設計

### 7.1 D3.js 圖譜渲染器

```javascript
// frontend/src/services/graphRenderer.js
import * as d3 from "d3";

export class KnowledgeGraphRenderer {
  constructor(container, width, height) {
    this.width = width;
    this.height = height;

    this.svg = d3
      .select(container)
      .append("svg")
      .attr("width", width)
      .attr("height", height);

    this.initializeSimulation();
  }

  initializeSimulation() {
    this.simulation = d3
      .forceSimulation()
      .force(
        "link",
        d3.forceLink().id((d) => d.id)
      )
      .force("charge", d3.forceManyBody().strength(-300))
      .force("center", d3.forceCenter(this.width / 2, this.height / 2))
      .force("collision", d3.forceCollide().radius(30));
  }

  render(data) {
    // 清除舊的渲染
    this.svg.selectAll("*").remove();

    // 添加箭頭標記
    this.addArrowMarkers();

    // 渲染邊
    const links = this.svg
      .append("g")
      .selectAll("line")
      .data(data.edges)
      .join("line")
      .attr("stroke", (d) => this.getEdgeColor(d.type))
      .attr("stroke-width", (d) => this.getEdgeWidth(d.type))
      .attr("marker-end", "url(#arrowhead)");

    // 渲染節點
    const nodes = this.svg
      .append("g")
      .selectAll("g")
      .data(data.nodes)
      .join("g")
      .call(this.drag());

    // 添加節點圖形
    nodes
      .append("circle")
      .attr("r", (d) => this.getNodeSize(d.type))
      .attr("fill", (d) => this.getNodeColor(d.type))
      .attr("stroke", "#fff")
      .attr("stroke-width", 2);

    // 添加節點圖標
    nodes
      .append("text")
      .attr("text-anchor", "middle")
      .attr("dominant-baseline", "middle")
      .attr("font-size", "16px")
      .text((d) => this.getNodeIcon(d.type));

    // 添加節點標籤
    nodes
      .append("text")
      .attr("text-anchor", "middle")
      .attr("y", 30)
      .attr("font-size", "12px")
      .text((d) => d.properties.name || d.id);

    // 添加邊標籤
    const edgeLabels = this.svg
      .append("g")
      .selectAll("text")
      .data(data.edges)
      .join("text")
      .attr("text-anchor", "middle")
      .attr("font-size", "10px")
      .text((d) => this.getEdgeLabel(d.type));

    // 更新模擬
    this.simulation.nodes(data.nodes);
    this.simulation.force("link").links(data.edges);

    this.simulation.on("tick", () => {
      links
        .attr("x1", (d) => d.source.x)
        .attr("y1", (d) => d.source.y)
        .attr("x2", (d) => d.target.x)
        .attr("y2", (d) => d.target.y);

      nodes.attr("transform", (d) => `translate(${d.x},${d.y})`);

      edgeLabels
        .attr("x", (d) => (d.source.x + d.target.x) / 2)
        .attr("y", (d) => (d.source.y + d.target.y) / 2);
    });
  }

  getNodeColor(type) {
    const colors = {
      Person: "#4A90E2",
      Case: "#E94B4B",
      Organization: "#50C878",
      Location: "#FF8C00",
      Law: "#9370DB",
      Event: "#FFD700",
    };
    return colors[type] || "#CCCCCC";
  }

  getNodeIcon(type) {
    const icons = {
      Person: "👤",
      Case: "📄",
      Organization: "🏢",
      Location: "📍",
      Law: "⚖️",
      Event: "⚡",
    };
    return icons[type] || "❓";
  }

  getEdgeColor(type) {
    const colors = {
      HAS_PARTICIPANT: "#666666",
      CITES_LAW: "#9370DB",
      BELONGS_TO: "#50C878",
      TARGET: "#E94B4B",
      IN_LOCATION: "#FF8C00",
      HAS_EVENT: "#FFD700",
    };
    return colors[type] || "#CCCCCC";
  }

  getEdgeWidth(type) {
    const widths = {
      HAS_PARTICIPANT: 2,
      CITES_LAW: 3,
      BELONGS_TO: 2,
      TARGET: 2,
      IN_LOCATION: 1,
      HAS_EVENT: 2,
    };
    return widths[type] || 1;
  }

  getNodeSize(type) {
    const sizes = {
      Person: 20,
      Case: 25,
      Organization: 20,
      Location: 15,
      Law: 20,
      Event: 20,
    };
    return sizes[type] || 15;
  }

  getEdgeLabel(type) {
    const labels = {
      HAS_PARTICIPANT: "參與",
      CITES_LAW: "引用法條",
      BELONGS_TO: "隸屬於",
      TARGET: "受害者",
      IN_LOCATION: "發生地點",
      HAS_EVENT: "相關事件",
    };
    return labels[type] || type;
  }

  addArrowMarkers() {
    const defs = this.svg.append("defs");

    defs
      .append("marker")
      .attr("id", "arrowhead")
      .attr("viewBox", "0 -5 10 10")
      .attr("refX", 8)
      .attr("refY", 0)
      .attr("orient", "auto")
      .attr("markerWidth", 6)
      .attr("markerHeight", 6)
      .append("path")
      .attr("d", "M0,-5L10,0L0,5")
      .attr("fill", "#666");
  }

  drag() {
    return d3
      .drag()
      .on("start", (event, d) => {
        if (!event.active) this.simulation.alphaTarget(0.3).restart();
        d.fx = d.x;
        d.fy = d.y;
      })
      .on("drag", (event, d) => {
        d.fx = event.x;
        d.fy = event.y;
      })
      .on("end", (event, d) => {
        if (!event.active) this.simulation.alphaTarget(0);
        d.fx = null;
        d.fy = null;
      });
  }
}
```

### 7.2 Vue.js 圖譜組件

```vue
<!-- frontend/src/components/KnowledgeGraph/GraphContainer.vue -->
<template>
  <div class="graph-container">
    <div class="graph-header">
      <h3>知識圖譜</h3>
      <div class="graph-controls">
        <el-button @click="resetZoom" size="small">重置縮放</el-button>
        <el-button @click="exportGraph" size="small">匯出圖片</el-button>
      </div>
    </div>

    <div class="graph-content">
      <div ref="graphContainer" class="graph-renderer"></div>

      <div class="graph-legend">
        <div class="legend-title">圖例</div>
        <div class="legend-items">
          <div
            v-for="nodeType in nodeTypes"
            :key="nodeType.type"
            class="legend-item"
          >
            <div
              class="legend-icon"
              :style="{ backgroundColor: nodeType.color }"
            >
              {{ nodeType.icon }}
            </div>
            <span>{{ nodeType.label }}</span>
          </div>
        </div>
      </div>
    </div>

    <div class="graph-info" v-if="selectedNode">
      <h4>{{ selectedNode.properties.name }}</h4>
      <div class="node-properties">
        <div
          v-for="(value, key) in selectedNode.properties"
          :key="key"
          class="property-item"
        >
          <strong>{{ key }}:</strong> {{ value }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from "vue";
import { KnowledgeGraphRenderer } from "@/services/graphRenderer";

const props = defineProps({
  graphData: {
    type: Object,
    required: true,
  },
});

const graphContainer = ref();
const selectedNode = ref(null);
const renderer = ref(null);

const nodeTypes = [
  { type: "Person", label: "人物", color: "#4A90E2", icon: "👤" },
  { type: "Case", label: "案件", color: "#E94B4B", icon: "📄" },
  { type: "Organization", label: "組織", color: "#50C878", icon: "🏢" },
  { type: "Location", label: "地點", color: "#FF8C00", icon: "📍" },
  { type: "Law", label: "法條", color: "#9370DB", icon: "⚖️" },
  { type: "Event", label: "事件", color: "#FFD700", icon: "⚡" },
];

onMounted(() => {
  if (graphContainer.value) {
    renderer.value = new KnowledgeGraphRenderer(graphContainer.value, 800, 600);

    if (props.graphData) {
      renderer.value.render(props.graphData);
    }
  }
});

watch(
  () => props.graphData,
  (newData) => {
    if (renderer.value && newData) {
      renderer.value.render(newData);
    }
  },
  { deep: true }
);

const resetZoom = () => {
  // 重置圖譜縮放
};

const exportGraph = () => {
  // 匯出圖譜為圖片
};
</script>

<style scoped>
.graph-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.graph-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #eee;
}

.graph-content {
  flex: 1;
  position: relative;
}

.graph-renderer {
  width: 100%;
  height: 100%;
}

.graph-legend {
  position: absolute;
  top: 16px;
  right: 16px;
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.legend-title {
  font-weight: bold;
  margin-bottom: 8px;
}

.legend-item {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
}

.legend-icon {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 8px;
  font-size: 12px;
}

.graph-info {
  position: absolute;
  bottom: 16px;
  left: 16px;
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  max-width: 300px;
}

.property-item {
  margin-bottom: 4px;
}
</style>
```

## 8. 實作優先級與階段規劃

### 階段一：基礎架構建立（2-3 週）

**後端基礎設施**

- [ ] FastAPI 專案初始化
- [ ] Neo4j 連接配置與測試
- [ ] 基本 API 路由結構
- [ ] Pydantic 模型整合
- [ ] 基礎錯誤處理機制

**前端基礎設施**

- [ ] Vue.js 3 專案設置
- [ ] Element Plus 整合
- [ ] 基本路由配置
- [ ] API 服務層建立
- [ ] 基本聊天界面

**整合測試**

- [ ] 前後端通信測試
- [ ] 基本資料流測試

### 階段二：核心功能開發（3-4 週）

**AI 查詢處理**

- [ ] OpenAI API 整合
- [ ] 自然語言查詢分析
- [ ] Cypher 查詢生成
- [ ] 查詢結果格式化
- [ ] 錯誤處理與重試機制

**知識圖譜視覺化**

- [ ] D3.js 基礎整合
- [ ] 節點與邊的基本渲染
- [ ] 節點類型視覺化設計
- [ ] 基本互動功能（點擊、拖拽）
- [ ] 圖譜佈局演算法

**聊天界面完善**

- [ ] 消息歷史管理
- [ ] 即時查詢狀態顯示
- [ ] 結果展示優化

### 階段三：功能完善與優化（2-3 週）

**進階視覺化功能**

- [ ] 圖譜縮放與平移
- [ ] 節點詳細資訊展示
- [ ] 圖譜導出功能
- [ ] 圖譜篩選與搜尋
- [ ] 動畫效果優化

**用戶體驗優化**

- [ ] 載入動畫與進度指示
- [ ] 錯誤訊息優化
- [ ] 響應式設計
- [ ] 無障礙設計

**效能優化**

- [ ] 查詢結果快取
- [ ] 大型圖譜分頁載入
- [ ] 前端效能優化
- [ ] 記憶體管理優化

### 階段四：測試與部署（1-2 週）

**測試**

- [ ] 後端單元測試
- [ ] API 整合測試
- [ ] 前端組件測試
- [ ] 端到端測試
- [ ] 效能測試

**部署準備**

- [ ] Docker 容器化
- [ ] 環境配置管理
- [ ] 部署腳本
- [ ] 監控與日誌

## 9. 碩士論文技術亮點

### 9.1 創新性與貢獻

**學術創新點**

1. **跨域技術整合**：首次將大語言模型、知識圖譜、自然語言處理應用於台灣法院判決書分析
2. **資料集貢獻**：建立結構化的台灣法院判決書知識圖譜資料集
3. **查詢方法創新**：提出基於自然語言的法律知識圖譜查詢方法
4. **視覺化創新**：設計專門針對法律關係的知識圖譜視覺化方案

**技術貢獻**

1. **架構設計**：完整的法律知識圖譜查詢系統架構
2. **AI 應用**：GPT-4 在法律查詢生成的實際應用
3. **資料處理**：法院判決書的自動化結構化處理流程
4. **用戶界面**：直觀的法律知識圖譜查詢界面

### 9.2 論文章節建議

**第一章：緒論**

- 研究背景與動機
- 研究目的與問題
- 研究範圍與限制
- 論文結構

**第二章：文獻探討**

- 法律資訊檢索技術
- 知識圖譜在法律領域的應用
- 大語言模型在法律分析中的應用
- 自然語言查詢技術

**第三章：系統設計與架構**

- 整體系統架構
- 資料模型設計
- 知識圖譜構建方法
- 查詢處理流程

**第四章：核心技術實現**

- 判決書資料萃取與結構化
- 知識圖譜建構技術
- 自然語言查詢處理
- 視覺化展示技術

**第五章：系統實作與評估**

- 系統實作細節
- 功能測試與驗證
- 效能評估
- 用戶體驗評估

**第六章：結論與未來工作**

- 研究成果總結
- 技術貢獻分析
- 研究限制與挑戰
- 未來發展方向

### 9.3 技術亮點總結

**AI 技術應用**

- 使用 GPT-4 進行複雜法律查詢的理解與轉換
- 實現自然語言到結構化查詢的智能轉換
- 提供基於語義理解的查詢結果解釋

**圖資料庫技術**

- 設計完整的法律領域知識圖譜 Schema
- 實現複雜法律關係的圖形化存儲與查詢
- 支援多層次、多維度的關係分析

**前端視覺化技術**

- 開發專業的法律知識圖譜視覺化組件
- 實現直觀的法律關係展示
- 支援互動式的圖譜探索

**系統架構設計**

- 前後端分離的現代化架構
- 微服務化的系統設計
- 高效能的查詢處理流程

## 10. 環境配置與部署

### 10.1 開發環境需求

**前端開發環境**

```bash
# Node.js版本要求
node >= 18.0.0
npm >= 9.0.0

# 主要依賴
- Vue.js 3.4+
- Vite 4.0+
- Element Plus 2.4+
- D3.js 7.8+
- JavaScript ES6+
```

**後端開發環境**

```bash
# Python版本要求
python >= 3.10

# 主要依賴
- FastAPI 0.104+
- Neo4j 5.0+
- OpenAI Python SDK 1.0+
- Pydantic 2.0+
- Uvicorn 0.24+
```

### 10.2 Docker 部署配置

```dockerfile
# 前端Dockerfile
FROM node:18-alpine AS builder
WORKDIR /app
COPY package*.json ./
RUN npm ci
COPY . .
RUN npm run build

FROM nginx:alpine
COPY --from=builder /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/conf.d/default.conf
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

```dockerfile
# 後端Dockerfile
FROM python:3.11-slim
WORKDIR /app
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt
COPY . .
EXPOSE 8000
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

```yaml
# docker-compose.yml
version: "3.8"
services:
  neo4j:
    image: neo4j:5.0
    ports:
      - "7474:7474"
      - "7687:7687"
    environment:
      NEO4J_AUTH: neo4j/password
      NEO4J_PLUGINS: '["apoc"]'
    volumes:
      - neo4j_data:/data

  backend:
    build: ./backend
    ports:
      - "8000:8000"
    depends_on:
      - neo4j
    environment:
      NEO4J_URI: bolt://neo4j:7687
      NEO4J_USER: neo4j
      NEO4J_PASSWORD: password
      OPENAI_API_KEY: your-openai-key

  frontend:
    build: ./frontend
    ports:
      - "3000:80"
    depends_on:
      - backend

volumes:
  neo4j_data:
```

## 11. 結論

本系統架構設計具有以下特點：

### 11.1 技術優勢

- **現代化技術棧**：採用 Vue.js 3、FastAPI、Neo4j 等先進技術
- **AI 驅動**：整合 OpenAI GPT-4 提供智能查詢能力
- **視覺化強化**：使用 D3.js 實現專業的知識圖譜視覺化
- **架構清晰**：前後端分離，模組化設計，易於維護和擴展

### 11.2 學術價值

- **創新性**：首個基於台灣法院判決書的知識圖譜查詢系統
- **實用性**：可應用於法律研究、案例分析、法律教育等領域
- **技術深度**：涵蓋 AI、資料庫、前端等多個技術領域
- **研究貢獻**：提供完整的法律知識圖譜構建與查詢方法

### 11.3 實作可行性

- **基礎完備**：基於現有的判決書分析系統
- **技術成熟**：所選技術都有豐富的生態系統和文檔
- **階段清晰**：分階段實作，風險可控
- **擴展性強**：架構設計支援未來功能擴展

這個架構設計為您的碩士論文提供了完整的技術藍圖，既有足夠的技術深度，又具備實際的應用價值。建議按照規劃的階段進行實作，確保每個階段都有明確的成果和驗證。

---

**文檔版本**：1.0  
**建立日期**：2024 年 1 月  
**最後更新**：2024 年 1 月  
**文檔狀態**：架構設計完成，準備開始實作
